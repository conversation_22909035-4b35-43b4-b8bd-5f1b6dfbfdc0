import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def find_short_perfect_periods(csv_file_path, period_days=7, min_devices=15, min_quality=95):
    """
    Find short periods with near-perfect data quality for spatial analysis.
    
    Parameters:
    csv_file_path (str): Path to the CSV file
    period_days (int): Length of period to analyze (days)
    min_devices (int): Minimum number of devices required
    min_quality (float): Minimum data quality percentage required
    
    Returns:
    dict: Analysis results with perfect periods
    """
    
    print("="*80)
    print(f"FINDING SHORT PERIODS WITH PERFECT DATA QUALITY")
    print(f"Target: {period_days} days, {min_devices}+ devices, {min_quality}%+ quality")
    print("="*80)
    
    # Load data
    df = pd.read_csv(csv_file_path)
    time_col = 'time_converted' if 'time_converted' in df.columns else 'cleaned_time'
    df[time_col] = pd.to_datetime(df[time_col])
    df['Temperature'] = pd.to_numeric(df['Temperature'], errors='coerce')
    df['Humidity'] = pd.to_numeric(df['Humidity'], errors='coerce')
    df = df.sort_values(time_col)
    
    print(f"Analyzing {len(df):,} records from {df[time_col].min().date()} to {df[time_col].max().date()}")
    print(f"Total devices available: {df['device_name'].nunique()}")
    
    # Get date range
    start_date = df[time_col].min().date()
    end_date = df[time_col].max().date()
    
    # ============================================================================
    # 1. ANALYZE ALL POSSIBLE PERIODS
    # ============================================================================
    
    print(f"\n1. ANALYZING ALL POSSIBLE {period_days}-DAY PERIODS...")
    
    perfect_periods = []
    current_date = start_date
    
    while current_date + timedelta(days=period_days-1) <= end_date:
        period_end = current_date + timedelta(days=period_days-1)
        
        # Filter data for this period
        period_data = df[
            (df[time_col].dt.date >= current_date) & 
            (df[time_col].dt.date <= period_end)
        ]
        
        if len(period_data) == 0:
            current_date += timedelta(days=1)
            continue
        
        # Analyze device quality for this period
        device_quality = {}
        
        for device in period_data['device_name'].unique():
            device_data = period_data[period_data['device_name'] == device]
            
            total_records = len(device_data)
            temp_valid = len(device_data.dropna(subset=['Temperature']))
            hum_valid = len(device_data.dropna(subset=['Humidity']))
            
            if total_records > 0:
                temp_completeness = (temp_valid / total_records) * 100
                hum_completeness = (hum_valid / total_records) * 100
                overall_quality = min(temp_completeness, hum_completeness)
                
                # Check for daily consistency (device should have data every day)
                daily_counts = device_data.groupby(device_data[time_col].dt.date).size()
                days_with_data = len(daily_counts[daily_counts > 0])
                day_consistency = (days_with_data / period_days) * 100
                
                # Final quality score
                final_quality = min(overall_quality, day_consistency)
                
                device_quality[device] = {
                    'total_records': total_records,
                    'temp_completeness': temp_completeness,
                    'hum_completeness': hum_completeness,
                    'day_consistency': day_consistency,
                    'final_quality': final_quality,
                    'days_with_data': days_with_data
                }
        
        # Find devices with excellent quality
        excellent_devices = [
            device for device, stats in device_quality.items() 
            if stats['final_quality'] >= min_quality
        ]
        
        if len(excellent_devices) >= min_devices:
            # Calculate period statistics
            period_stats = {
                'start_date': current_date,
                'end_date': period_end,
                'total_devices': len(device_quality),
                'excellent_devices': excellent_devices,
                'excellent_count': len(excellent_devices),
                'avg_quality': np.mean([stats['final_quality'] for stats in device_quality.values()]),
                'min_quality': min([stats['final_quality'] for device, stats in device_quality.items() if device in excellent_devices]),
                'total_records': len(period_data[period_data['device_name'].isin(excellent_devices)]),
                'device_quality': device_quality
            }
            
            perfect_periods.append(period_stats)
        
        current_date += timedelta(days=1)
    
    print(f"Found {len(perfect_periods)} periods meeting criteria")
    
    # ============================================================================
    # 2. RANK AND DISPLAY BEST PERIODS
    # ============================================================================
    
    if perfect_periods:
        print(f"\n2. RANKING PERFECT PERIODS...")
        
        # Sort by number of excellent devices, then by average quality
        perfect_periods.sort(key=lambda x: (x['excellent_count'], x['avg_quality']), reverse=True)
        
        print(f"\nTOP 10 PERFECT PERIODS:")
        for i, period in enumerate(perfect_periods[:10], 1):
            print(f"\n--- RANK {i} ---")
            print(f"Period: {period['start_date']} to {period['end_date']}")
            print(f"Excellent devices: {period['excellent_count']}/{period['total_devices']}")
            print(f"Quality: {period['min_quality']:.1f}% min, {period['avg_quality']:.1f}% avg")
            print(f"Total records: {period['total_records']:,}")
            print(f"Devices: {', '.join(period['excellent_devices'][:10])}{'...' if len(period['excellent_devices']) > 10 else ''}")
    
    else:
        print(f"\n❌ No periods found meeting criteria ({period_days} days, {min_devices}+ devices, {min_quality}%+ quality)")
        
        # Try with relaxed criteria
        print(f"\n🔍 Trying with relaxed criteria...")
        return find_short_perfect_periods(csv_file_path, period_days, min_devices-2, min_quality-5)
    
    # ============================================================================
    # 3. DETAILED ANALYSIS OF BEST PERIOD
    # ============================================================================
    
    if perfect_periods:
        best_period = perfect_periods[0]
        
        print(f"\n3. DETAILED ANALYSIS OF BEST PERIOD")
        print("="*50)
        print(f"Period: {best_period['start_date']} to {best_period['end_date']}")
        print(f"Duration: {period_days} days")
        
        # Get detailed device stats for best period
        print(f"\nDEVICE QUALITY BREAKDOWN:")
        device_stats = best_period['device_quality']
        excellent_devices = best_period['excellent_devices']
        
        # Sort devices by quality
        sorted_devices = sorted(device_stats.items(), key=lambda x: x[1]['final_quality'], reverse=True)
        
        print(f"EXCELLENT DEVICES (≥{min_quality}%):")
        for device, stats in sorted_devices:
            if device in excellent_devices:
                print(f"  {device:12s}: {stats['final_quality']:5.1f}% quality, "
                      f"{stats['total_records']:3d} records, "
                      f"{stats['days_with_data']}/{period_days} days")
        
        remaining_devices = [item for item in sorted_devices if item[0] not in excellent_devices]
        if remaining_devices:
            print(f"\nOTHER DEVICES (<{min_quality}%):")
            for device, stats in remaining_devices[:5]:  # Show top 5 remaining
                print(f"  {device:12s}: {stats['final_quality']:5.1f}% quality, "
                      f"{stats['total_records']:3d} records, "
                      f"{stats['days_with_data']}/{period_days} days")
        
        # Create the perfect dataset
        perfect_data = df[
            (df[time_col].dt.date >= best_period['start_date']) & 
            (df[time_col].dt.date <= best_period['end_date']) &
            (df['device_name'].isin(excellent_devices))
        ]
        
        print(f"\n📊 PERFECT DATASET SUMMARY:")
        print(f"   Period: {best_period['start_date']} to {best_period['end_date']}")
        print(f"   Duration: {period_days} days")
        print(f"   Devices: {len(excellent_devices)}")
        print(f"   Total records: {len(perfect_data):,}")
        print(f"   Records per device per day: {len(perfect_data) / (period_days * len(excellent_devices)):.1f}")
        print(f"   Data quality: {best_period['min_quality']:.1f}%+ for all devices")
        
        # Save the perfect dataset
        output_filename = f"perfect_spatial_data_{best_period['start_date']}_{best_period['end_date']}.csv"
        perfect_data.to_csv(output_filename, index=False)
        print(f"   ✅ Perfect dataset saved as: {output_filename}")
        
        return {
            'perfect_periods': perfect_periods,
            'best_period': best_period,
            'perfect_data': perfect_data,
            'excellent_devices': excellent_devices
        }
    
    return None

def try_multiple_period_lengths(csv_file_path, min_devices=15):
    """
    Try different period lengths to find the best option.
    
    Parameters:
    csv_file_path (str): Path to the CSV file
    min_devices (int): Minimum number of devices required
    """
    
    print("="*80)
    print("TESTING MULTIPLE PERIOD LENGTHS")
    print("="*80)
    
    period_options = [3, 5, 7, 10, 14]  # Different period lengths to try
    results = {}
    
    for days in period_options:
        print(f"\n🔍 Testing {days}-day periods...")
        try:
            result = find_short_perfect_periods(csv_file_path, period_days=days, min_devices=min_devices, min_quality=95)
            if result and result['perfect_periods']:
                results[days] = {
                    'count': len(result['perfect_periods']),
                    'best_devices': result['best_period']['excellent_count'],
                    'best_quality': result['best_period']['min_quality'],
                    'best_records': result['best_period']['total_records']
                }
                print(f"✅ Found {results[days]['count']} perfect {days}-day periods")
                print(f"   Best: {results[days]['best_devices']} devices, {results[days]['best_quality']:.1f}% quality")
            else:
                print(f"❌ No suitable {days}-day periods found")
        except:
            print(f"❌ Error analyzing {days}-day periods")
    
    # Summary
    print(f"\n📊 SUMMARY OF PERIOD LENGTH OPTIONS:")
    print("-" * 60)
    print(f"{'Days':<6} {'Periods':<8} {'Devices':<8} {'Quality':<8} {'Records':<8}")
    print("-" * 60)
    
    for days, stats in results.items():
        print(f"{days:<6} {stats['count']:<8} {stats['best_devices']:<8} {stats['best_quality']:<8.1f} {stats['best_records']:<8}")
    
    if results:
        # Recommend best option
        best_option = max(results.items(), key=lambda x: (x[1]['best_devices'], x[1]['best_quality']))
        print(f"\n🎯 RECOMMENDATION: Use {best_option[0]}-day periods")
        print(f"   {best_option[1]['count']} perfect periods available")
        print(f"   {best_option[1]['best_devices']} excellent devices")
        print(f"   {best_option[1]['best_quality']:.1f}% minimum quality")
        
        return best_option[0]
    
    return None

def create_perfect_period_visualization(analysis_results, csv_file_path):
    """
    Visualize the perfect periods found.
    """
    
    if not analysis_results:
        print("No analysis results to visualize")
        return
    
    perfect_periods = analysis_results['perfect_periods']
    best_period = analysis_results['best_period']
    perfect_data = analysis_results['perfect_data']
    time_col = 'time_converted' if 'time_converted' in perfect_data.columns else 'cleaned_time'
    
    fig, axes = plt.subplots(2, 2, figsize=(18, 12))
    fig.suptitle(f'Perfect Period Analysis - {best_period["start_date"]} to {best_period["end_date"]}', 
                 fontsize=16, fontweight='bold')
    
    # 1. Perfect periods timeline
    ax1 = axes[0, 0]
    
    dates = [p['start_date'] for p in perfect_periods]
    device_counts = [p['excellent_count'] for p in perfect_periods]
    
    ax1.scatter(dates, device_counts, alpha=0.7, s=50, color='green')
    ax1.set_title(f'Perfect Periods Found ({len(perfect_periods)} total)')
    ax1.set_ylabel('Number of Excellent Devices')
    ax1.set_xlabel('Period Start Date')
    ax1.grid(True, alpha=0.3)
    
    # Highlight best period
    best_date = best_period['start_date']
    best_count = best_period['excellent_count']
    ax1.scatter([best_date], [best_count], color='red', s=100, marker='*', label='Best Period')
    ax1.legend()
    
    # 2. Device quality in best period
    ax2 = axes[0, 1]
    
    device_stats = best_period['device_quality']
    excellent_devices = best_period['excellent_devices']
    
    devices = list(device_stats.keys())
    qualities = [device_stats[d]['final_quality'] for d in devices]
    colors = ['green' if d in excellent_devices else 'red' for d in devices]
    
    bars = ax2.bar(range(len(devices)), qualities, color=colors, alpha=0.7)
    ax2.set_title('Device Quality in Best Period')
    ax2.set_ylabel('Quality (%)')
    ax2.set_xlabel('Device')
    ax2.set_xticks(range(len(devices)))
    ax2.set_xticklabels(devices, rotation=45, ha='right')
    ax2.axhline(y=95, color='orange', linestyle='--', alpha=0.7, label='Quality Threshold')
    ax2.legend()
    
    # 3. Temperature data in best period
    ax3 = axes[1, 0]
    
    excellent_devices_list = analysis_results['excellent_devices']
    colors = plt.cm.tab10(np.linspace(0, 1, len(excellent_devices_list)))
    
    for i, device in enumerate(excellent_devices_list[:10]):  # Show top 10 devices
        device_data = perfect_data[perfect_data['device_name'] == device].dropna(subset=['Temperature'])
        if len(device_data) > 0:
            ax3.plot(device_data[time_col], device_data['Temperature'], 
                    color=colors[i], alpha=0.7, linewidth=1, label=device)
    
    ax3.set_title('Temperature Data - Best Period')
    ax3.set_ylabel('Temperature (°C)')
    ax3.set_xlabel('Date')
    ax3.grid(True, alpha=0.3)
    ax3.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
    
    # 4. Humidity data in best period
    ax4 = axes[1, 1]
    
    for i, device in enumerate(excellent_devices_list[:10]):  # Show top 10 devices
        device_data = perfect_data[perfect_data['device_name'] == device].dropna(subset=['Humidity'])
        if len(device_data) > 0:
            ax4.plot(device_data[time_col], device_data['Humidity'], 
                    color=colors[i], alpha=0.7, linewidth=1, label=device)
    
    ax4.set_title('Humidity Data - Best Period')
    ax4.set_ylabel('Humidity (%)')
    ax4.set_xlabel('Date')
    ax4.grid(True, alpha=0.3)
    ax4.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
    
    plt.tight_layout()
    plt.savefig('perfect_period_analysis.png', dpi=300, bbox_inches='tight')
    print("✅ Perfect period analysis saved as 'perfect_period_analysis.png'")
    plt.show()

# ============================================================================
# USAGE
# ============================================================================

if __name__ == "__main__":
    csv_file_path = "spatial_analysis_data_20250212_20250514.csv"
    
    print("🎯 FINDING SHORT PERIODS WITH PERFECT DATA QUALITY")
    print("This approach focuses on quality over quantity for spatial analysis")
    
    # Option 1: Try multiple period lengths to find best option
    print("\n" + "="*60)
    print("OPTION 1: AUTO-DETECT BEST PERIOD LENGTH")
    print("="*60)
    
    best_length = try_multiple_period_lengths(csv_file_path, min_devices=15)
    
    if best_length:
        print(f"\n🚀 Running detailed analysis with {best_length}-day periods...")
        results = find_short_perfect_periods(csv_file_path, period_days=best_length, min_devices=15, min_quality=95)
        
        if results:
            create_perfect_period_visualization(results, csv_file_path)
        
    # Option 2: Manual specification
    print("\n" + "="*60)
    print("OPTION 2: MANUAL SPECIFICATION")
    print("="*60)
    print("Uncomment and modify the following lines to manually specify criteria:")
    print("# results = find_short_perfect_periods(csv_file_path, period_days=7, min_devices=12, min_quality=90)")
    print("# create_perfect_period_visualization(results, csv_file_path)")
