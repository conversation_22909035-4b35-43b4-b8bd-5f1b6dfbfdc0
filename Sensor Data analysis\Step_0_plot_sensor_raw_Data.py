import pandas as pd
import matplotlib.pyplot as plt



import plotly.graph_objects as go
from plotly.offline import plot  # Import plot from plotly.offline

# from shiny.express import input, render, ui
# from shiny import App, render, ui

import time
import pandas as pd
import csv

import matplotlib.colors as mcolors
# from mycolorpy import colorlist as mcp


import numpy as np
import random, time
from datetime import datetime

# from sklearn.preprocessing import StandardScaler


import matplotlib.pyplot as plt


# from mpl_toolkits.mplot3d import Axes3D


# df = pd.read_csv(r'Data\filtered_devices_iot_sensor_data - Sheet1.csv')

# df = pd.read_csv(r'Data\filtered_devices_iot_sensor_data-2025-04-29.csv')

df = pd.read_csv(r'Data\filtered_devices_iot_sensor_data_2025-06-06.csv')



# Step 1: Load data
data = df

data["cleaned_time"] = data["time"].str.replace(r" \(.*\)", "", regex=True)

# Convert the cleaned time to datetime
data["time_converted"] = pd.to_datetime(data["cleaned_time"], errors="coerce")


data["time_converted"] = pd.to_datetime(
    data["cleaned_time"],
    errors="coerce",
    utc=True             # ← ensures the “+05:00” becomes a timezone‐aware datetime
)

# 3. Drop any rows where parsing failed
n_bad = data["time_converted"].isna().sum()
print(f"Dropping {n_bad} rows with invalid timestamps")



# Check for invalid rows
# invalid_rows = df[df["time_converted"].isna()]

data = data.dropna(subset=["time_converted"])
data.drop(columns=["time", "Bat_status", "Prob_temperature", "cleaned_time"], inplace=True)

data["Temperature"] = pd.to_numeric(data["Temperature"], errors="coerce")
data["Humidity"] = pd.to_numeric(data["Humidity"], errors="coerce")


data = data.dropna(subset=["Temperature", "Humidity"])


data["hourly_time"] = data["time_converted"].dt.floor("H")

df.to_csv('Data/filtered_devices_iot_sensor_data_2025-06-06_corrected_time.csv')




unique_sensors = df["device_name"].unique()
print("Unique Sensors:", unique_sensors)

#########################################
#########################################
#########################################


# Step 2: Calculate averages and standard deviations
grouped = data.groupby("hourly_time").agg(
    avg_temp=("Temperature", "mean"),
    std_temp=("Temperature", "std"),
    avg_humidity=("Humidity", "mean"),
    std_humidity=("Humidity", "std"),
    min_temp=("Temperature", "min"),
    max_temp=("Temperature", "max"),
).reset_index()





grouped = grouped.dropna(subset=["std_temp", "std_temp"])






# Create a figure
fig1 = go.Figure()

# Add traces for Temperature and Humidity
fig1.add_trace(go.Scatter(
    x=grouped["hourly_time"],  # Use your converted time
    y=grouped["avg_temp"],     # Use average temperature
    mode='lines+markers',      # Display lines and markers
    name='Average Temperature',
    line=dict(color='blue')
))

fig1.add_trace(go.Scatter(
    x=grouped["hourly_time"],  # Use your converted time
    y=grouped["avg_humidity"], # Use average humidity
    mode='lines+markers',      # Display lines and markers
    name='Average Humidity',
    line=dict(color='green')
))

# Customize layout
fig1.update_layout(
    title="Average Temperature and Humidity Over Time",
    xaxis_title="Time",
    yaxis_title="Value",
    template="plotly_white",   # Optional: You can change the plot theme
    legend_title="Sensors"
)

# Display the plot in the browser
plot(fig1, filename="temperature_Humidity")



########################################################
########################################################
########################################################
fig2 = go.Figure()

# Add the average temperature as a line
fig2.add_trace(go.Scatter(
    x=grouped["hourly_time"],  # Use your converted time
    y=grouped["avg_temp"],     # Average temperature
    mode='lines',              # Just the line
    name='Average Temperature',
    line=dict(color='blue')
))

# Add the shaded region for the standard deviation buffer
fig2.add_trace(go.Scatter(
    x=grouped["hourly_time"],
    y=grouped["avg_temp"] + grouped["std_temp"],  # Upper bound (avg_temp + std_temp)
    fill=None,  # No fill on the top boundary
    mode='lines',
    line=dict(color='rgba(0,0,0,0)'),  # Transparent line for the upper bound
    showlegend=False  # Don't show this trace in the legend
))

fig2.add_trace(go.Scatter(
    x=grouped["hourly_time"],
    y=grouped["avg_temp"] - grouped["std_temp"],  # Lower bound (avg_temp - std_temp)
    fill='tonexty',  # Fill the region between the two traces
    mode='lines',
    line=dict(color='rgba(0,0,0,0)'),  # Transparent line for the lower bound
    fillcolor='rgba(0,0,255,0.2)',  # Light blue color for the shaded region
    name='Temperature Std Dev Buffer'  # Optional name for the shaded region
))

# Customize layout
fig2.update_layout(
    title="Average Temperature with Standard Deviation Buffer",
    xaxis_title="Time",
    yaxis_title="Temperature (°C)",
    template="plotly_white",   # Optional: You can change the plot theme
    legend_title="Sensors"
)

# Display the plot in the browser
plot(fig2, filename="Temperature_STD")


########################################################
########################################################
########################################################

fig3 = go.Figure()

# Add the average temperature as a line
fig3.add_trace(go.Scatter(
    x=grouped["hourly_time"],  # Use your converted time
    y=grouped["avg_humidity"],     # Average temperature
    mode='lines',              # Just the line
    name='Average Humidity',
    line=dict(color='blue')
))

# Add the shaded region for the standard deviation buffer
fig3.add_trace(go.Scatter(
    x=grouped["hourly_time"],
    y=grouped["avg_humidity"] + grouped["std_humidity"],  # Upper bound (avg_temp + std_temp)
    fill=None,  # No fill on the top boundary
    mode='lines',
    line=dict(color='rgba(0,0,0,0)'),  # Transparent line for the upper bound
    showlegend=False  # Don't show this trace in the legend
))

fig3.add_trace(go.Scatter(
    x=grouped["hourly_time"],
    y=grouped["avg_humidity"] - grouped["std_humidity"],  # Lower bound (avg_temp - std_temp)
    fill='tonexty',  # Fill the region between the two traces
    mode='lines',
    line=dict(color='rgba(0,0,0,0)'),  # Transparent line for the lower bound
    fillcolor='rgba(0,0,255,0.2)',  # Light blue color for the shaded region
    name='Humidity Std Dev Buffer'  # Optional name for the shaded region
))

# Customize layout
fig3.update_layout(
    title="Average Humidity with Standard Deviation Buffer",
    xaxis_title="Time",
    yaxis_title="Humidity (%RH)",
    template="plotly_white",   # Optional: You can change the plot theme
    legend_title="Sensors"
)

# Display the plot in the browser
plot(fig3, filename="Humidity_STD")


########################################
########################################
########################################
fig_min_max = go.Figure()

# Plot min and max temperature range as shaded area
fig_min_max.add_trace(go.Scatter(
    x=grouped["hourly_time"],
    y=grouped["max_temp"],  # Max temperature
    mode='lines+markers', 
    name="Max Temperature",
    line=dict(color='red')
))

fig_min_max.add_trace(go.Scatter(
    x=grouped["hourly_time"],
    y=grouped["min_temp"],  # Min temperature
    mode='lines+markers', 
    name="Min Temperature",
    line=dict(color='green')
))

# Plot filled area between min and max
fig_min_max.add_trace(go.Scatter(
    x=grouped["hourly_time"],
    y=grouped["max_temp"],
    mode='lines',
    line=dict(width=0),
    fill='tonexty',  # Fill between the lines
    fillcolor='rgba(255, 0, 0, 0.2)'  # Semi-transparent red
))

# fig_min_max.add_trace(go.Scatter(
#     x=grouped["hourly_time"],
#     y=grouped["min_temp"],
#     mode='lines',
#     line=dict(width=0),
#     fill='tonexty',  # Fill between the lines
#     fillcolor='rgba(0, 255, 0, 0.2)'  # Semi-transparent green
# ))

# Update layout for min-max range graph
fig_min_max.update_layout(
    title="Temperature Range (Min-Max) for Each Hour",
    xaxis_title="Time",
    yaxis_title="Temperature (°C)",
    showlegend=True
)

plot(fig_min_max, filename="temperature_min_max.html")



########################################
########################################
########################################


def random_color():
    return f'rgba({random.randint(0, 255)}, {random.randint(0, 255)}, {random.randint(0, 255)}, 1)'

fig_all_temperatures2 = go.Figure()

for sensor in unique_sensors:
    sensor_data = data[data["device_name"] == sensor]  # No aggregation, raw data
    
    
    fig_all_temperatures2.add_trace(go.Scatter(
        x=sensor_data["time_converted"],  # Use your converted time
        y=sensor_data["Temperature"],     # Use average temperature
        mode='lines+markers',      # Display lines and markers
        # mode='lines',
        name=sensor,
        line=dict(color=random_color())
    ))
    
    time.sleep(0.2)
    
fig_all_temperatures2.update_layout(
    title="Temperature Time Series for All Sensors",
    xaxis_title="Time",
    yaxis_title="Temperature (°C)",
    legend_title="Temperature",
    template="plotly_white",   
    # showlegend=True
)


plot(fig_all_temperatures2, filename="all_sensors_temperature.html")


########################################
########################################
########################################
# Function to export hourly averages to CSV

def export_hourly_averages(start_time, end_time, output_filename):
    """
    Export hourly averages of temperature and humidity to CSV file.

    Parameters:
    start_time (str): Start datetime in format '2025-03-10 07:00:00+04:00'
    end_time (str): End datetime in format '2025-03-19 00:00:00+04:00'
    output_filename (str): Name of the output CSV file
    """
    # Convert string timestamps to datetime objects
    start_dt = pd.to_datetime(start_time)
    end_dt = pd.to_datetime(end_time)

    # Filter the grouped data by the specified datetime range
    filtered_data = grouped[
        (grouped["hourly_time"] >= start_dt) &
        (grouped["hourly_time"] <= end_dt)
    ].copy()

    # Create the export dataframe with the required format
    export_df = pd.DataFrame({
        'timestamp': filtered_data["hourly_time"],
        'Temperature_Average': filtered_data["avg_temp"].round(2),
        'Humidity_Average': filtered_data["avg_humidity"].round(2)
    })

    # Export to CSV
    export_df.to_csv(output_filename, index=False)
    print(f"Exported {len(export_df)} hourly averages to {output_filename}")
    print(f"Date range: {start_time} to {end_time}")

    return export_df

# Export the specified data range
export_hourly_averages(
    start_time='2025-03-10 07:00:00+04:00',
    end_time='2025-03-19 00:00:00+04:00',
    output_filename='hourly_averages_export.csv'
)



# fig, ax1 = plt.subplots(figsize=(12, 6))

# # Plot raw temperature data
# ax1.set_xlabel("Time")
# ax1.set_ylabel("Temperature (°C)", color="tab:red")
# ax1.plot(sensor_data["time_converted"], sensor_data["Temperature"], color="tab:red", label="Temperature")
# ax1.tick_params(axis="y", labelcolor="tab:red")

# # Plot raw humidity data
# ax2 = ax1.twinx()
# ax2.set_ylabel("Humidity (%)", color="tab:blue")
# ax2.plot(sensor_data["time_converted"], sensor_data["Humidity"], color="tab:blue", linestyle="dashed", label="Humidity")
# ax2.tick_params(axis="y", labelcolor="tab:blue")

# plt.title(f"Sensor: {sensor}")
# fig.tight_layout()
# plt.show()