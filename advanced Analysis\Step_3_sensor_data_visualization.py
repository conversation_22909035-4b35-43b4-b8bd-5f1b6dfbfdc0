import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import matplotlib.dates as mdates
from matplotlib.widgets import CheckButtons
import warnings
warnings.filterwarnings('ignore')

def load_and_prepare_spatial_data(csv_file_path):
    """
    Load and prepare the spatial analysis data.
    
    Parameters:
    csv_file_path (str): Path to the spatial analysis CSV file
    
    Returns:
    pd.DataFrame: Prepared data with datetime conversion
    """
    print("Loading spatial analysis data...")
    
    df = pd.read_csv(csv_file_path)
    
    # Convert time column
    time_col = 'time_converted' if 'time_converted' in df.columns else 'cleaned_time'
    df[time_col] = pd.to_datetime(df[time_col])
    df['Temperature'] = pd.to_numeric(df['Temperature'], errors='coerce')
    df['Humidity'] = pd.to_numeric(df['Humidity'], errors='coerce')
    
    # Sort by time
    df = df.sort_values(time_col)
    
    print(f"✓ Loaded {len(df):,} records")
    print(f"✓ Period: {df[time_col].min().date()} to {df[time_col].max().date()}")
    print(f"✓ Devices: {df['device_name'].nunique()}")
    print(f"✓ Device list: {', '.join(sorted(df['device_name'].unique()))}")
    
    return df, time_col

def create_individual_sensor_plots(df, time_col, device_list=None, save_plots=False):
    """
    Create individual plots for each sensor.
    
    Parameters:
    df (pd.DataFrame): The sensor data
    time_col (str): Name of the time column
    device_list (list): List of devices to plot (None for all)
    save_plots (bool): Whether to save plots
    """
    
    if device_list is None:
        device_list = sorted(df['device_name'].unique())
    
    print(f"\nCreating individual sensor plots for {len(device_list)} devices...")
    
    # Create subplots - arrange in a grid
    n_devices = len(device_list)
    cols = 3
    rows = (n_devices + cols - 1) // cols
    
    fig, axes = plt.subplots(rows, cols, figsize=(20, 4 * rows))
    fig.suptitle('Individual Sensor Data - Temperature and Humidity Over Time', fontsize=16, fontweight='bold')
    
    # Flatten axes array for easy indexing
    if rows == 1:
        axes = [axes] if cols == 1 else axes
    else:
        axes = axes.flatten()
    
    for idx, device in enumerate(device_list):
        if idx >= len(axes):
            break
            
        ax = axes[idx]
        device_data = df[df['device_name'] == device].copy()
        
        # Create dual axis plot
        ax2 = ax.twinx()
        
        # Plot temperature
        temp_data = device_data.dropna(subset=['Temperature'])
        if len(temp_data) > 0:
            ax.plot(temp_data[time_col], temp_data['Temperature'], 
                   color='red', alpha=0.7, linewidth=1, label='Temperature')
            ax.set_ylabel('Temperature (°C)', color='red')
            ax.tick_params(axis='y', labelcolor='red')
        
        # Plot humidity
        hum_data = device_data.dropna(subset=['Humidity'])
        if len(hum_data) > 0:
            ax2.plot(hum_data[time_col], hum_data['Humidity'], 
                    color='blue', alpha=0.7, linewidth=1, label='Humidity')
            ax2.set_ylabel('Humidity (%)', color='blue')
            ax2.tick_params(axis='y', labelcolor='blue')
        
        ax.set_title(f'{device}', fontweight='bold')
        ax.grid(True, alpha=0.3)
        
        # Format x-axis
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        # Add statistics text
        if len(temp_data) > 0 and len(hum_data) > 0:
            stats_text = f"Temp: {temp_data['Temperature'].mean():.1f}°C avg\nHum: {hum_data['Humidity'].mean():.1f}% avg\nRecords: {len(device_data)}"
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
                   verticalalignment='top', fontsize=8, 
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    # Hide empty subplots
    for idx in range(len(device_list), len(axes)):
        axes[idx].set_visible(False)
    
    plt.tight_layout()
    
    if save_plots:
        plt.savefig('individual_sensor_plots.png', dpi=300, bbox_inches='tight')
        print("✓ Individual sensor plots saved as 'individual_sensor_plots.png'")
    
    plt.show()

def create_multi_sensor_comparison(df, time_col, metric='Temperature', device_list=None, save_plots=False):
    """
    Create multi-sensor comparison plots.
    
    Parameters:
    df (pd.DataFrame): The sensor data
    time_col (str): Name of the time column
    metric (str): 'Temperature' or 'Humidity'
    device_list (list): List of devices to include
    save_plots (bool): Whether to save plots
    """
    
    if device_list is None:
        device_list = sorted(df['device_name'].unique())
    
    print(f"\nCreating multi-sensor {metric.lower()} comparison...")
    
    fig, axes = plt.subplots(2, 2, figsize=(20, 12))
    fig.suptitle(f'Multi-Sensor {metric} Analysis', fontsize=16, fontweight='bold')
    
    # 1. Time series overlay
    ax1 = axes[0, 0]
    colors = plt.cm.tab20(np.linspace(0, 1, len(device_list)))
    
    for i, device in enumerate(device_list):
        device_data = df[df['device_name'] == device].dropna(subset=[metric])
        if len(device_data) > 0:
            ax1.plot(device_data[time_col], device_data[metric], 
                    color=colors[i], alpha=0.7, linewidth=1, label=device)
    
    ax1.set_title(f'{metric} Time Series - All Sensors')
    ax1.set_ylabel(f'{metric} ({"°C" if metric == "Temperature" else "%"})')
    ax1.grid(True, alpha=0.3)
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
    
    # Format x-axis
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
    ax1.xaxis.set_major_locator(mdates.WeekdayLocator(interval=1))
    plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
    
    # 2. Box plots by device
    ax2 = axes[0, 1]
    device_values = []
    device_names = []
    
    for device in device_list:
        device_data = df[df['device_name'] == device].dropna(subset=[metric])
        if len(device_data) > 0:
            device_values.append(device_data[metric].values)
            device_names.append(device)
    
    if device_values:
        ax2.boxplot(device_values, labels=device_names)
        ax2.set_title(f'{metric} Distribution by Device')
        ax2.set_ylabel(f'{metric} ({"°C" if metric == "Temperature" else "%"})')
        plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45, ha='right')
    
    # 3. Daily averages heatmap
    ax3 = axes[1, 0]
    
    # Create daily averages matrix
    df_metric = df.dropna(subset=[metric])
    df_metric['date'] = df_metric[time_col].dt.date
    
    daily_avg = df_metric.groupby(['date', 'device_name'])[metric].mean().unstack()
    
    if not daily_avg.empty:
        # Sample every few days for better visualization
        sample_dates = daily_avg.index[::3]  # Every 3rd day
        sampled_data = daily_avg.loc[sample_dates]
        
        sns.heatmap(sampled_data.T, ax=ax3, cmap='RdYlBu_r' if metric == 'Temperature' else 'Blues',
                   cbar_kws={'label': f'{metric} ({"°C" if metric == "Temperature" else "%"})'},
                   xticklabels=[d.strftime('%m-%d') for d in sample_dates[::2]])
        
        ax3.set_title(f'Daily Average {metric} Heatmap')
        ax3.set_xlabel('Date')
        ax3.set_ylabel('Device')
    
    # 4. Device correlation matrix
    ax4 = axes[1, 1]
    
    # Create correlation matrix
    pivot_data = df.dropna(subset=[metric]).pivot_table(
        index=time_col, columns='device_name', values=metric, aggfunc='mean'
    )
    
    if not pivot_data.empty:
        correlation_matrix = pivot_data.corr()
        
        sns.heatmap(correlation_matrix, ax=ax4, annot=False, cmap='coolwarm', center=0,
                   cbar_kws={'label': 'Correlation Coefficient'})
        ax4.set_title(f'{metric} Correlation Between Devices')
        plt.setp(ax4.xaxis.get_majorticklabels(), rotation=45, ha='right')
        plt.setp(ax4.yaxis.get_majorticklabels(), rotation=0)
    
    plt.tight_layout()
    
    if save_plots:
        plt.savefig(f'multi_sensor_{metric.lower()}_analysis.png', dpi=300, bbox_inches='tight')
        print(f"✓ Multi-sensor {metric.lower()} analysis saved as 'multi_sensor_{metric.lower()}_analysis.png'")
    
    plt.show()

def create_data_quality_visualization(df, time_col, save_plots=False):
    """
    Create visualizations focused on data quality assessment.
    
    Parameters:
    df (pd.DataFrame): The sensor data
    time_col (str): Name of the time column
    save_plots (bool): Whether to save plots
    """
    
    print("\nCreating data quality visualizations...")
    
    fig, axes = plt.subplots(2, 2, figsize=(18, 12))
    fig.suptitle('Data Quality Assessment', fontsize=16, fontweight='bold')
    
    # 1. Data availability over time
    ax1 = axes[0, 0]
    
    # Create daily data count matrix
    df['date'] = df[time_col].dt.date
    daily_counts = df.groupby(['date', 'device_name']).size().unstack(fill_value=0)
    
    # Plot data availability
    dates = daily_counts.index
    devices = daily_counts.columns
    
    for i, device in enumerate(devices):
        ax1.plot(dates, daily_counts[device], alpha=0.7, linewidth=1, label=device)
    
    ax1.set_title('Daily Data Records per Device')
    ax1.set_ylabel('Number of Records')
    ax1.set_xlabel('Date')
    ax1.grid(True, alpha=0.3)
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
    
    # 2. Missing data heatmap
    ax2 = axes[0, 1]
    
    # Create missing data matrix (1 = missing, 0 = present)
    missing_matrix = daily_counts.copy()
    missing_matrix[missing_matrix > 0] = 1  # Has data
    missing_matrix[missing_matrix == 0] = 0  # No data
    
    # Sample for better visualization
    sample_dates = missing_matrix.index[::3]  # Every 3rd day
    sampled_missing = missing_matrix.loc[sample_dates]
    
    sns.heatmap(sampled_missing.T, ax=ax2, cmap='RdYlGn', cbar_kws={'label': 'Data Available (1=Yes, 0=No)'},
               xticklabels=[d.strftime('%m-%d') for d in sample_dates[::2]])
    ax2.set_title('Data Availability Heatmap')
    ax2.set_xlabel('Date')
    ax2.set_ylabel('Device')
    
    # 3. Temperature data quality by device
    ax3 = axes[1, 0]
    
    temp_quality = []
    device_names = []
    
    for device in sorted(df['device_name'].unique()):
        device_data = df[df['device_name'] == device]
        total_records = len(device_data)
        valid_temp = len(device_data.dropna(subset=['Temperature']))
        valid_hum = len(device_data.dropna(subset=['Humidity']))
        
        if total_records > 0:
            temp_completeness = (valid_temp / total_records) * 100
            hum_completeness = (valid_hum / total_records) * 100
            
            temp_quality.append([temp_completeness, hum_completeness])
            device_names.append(device)
    
    if temp_quality:
        temp_quality = np.array(temp_quality)
        
        x = np.arange(len(device_names))
        width = 0.35
        
        ax3.bar(x - width/2, temp_quality[:, 0], width, label='Temperature', alpha=0.8, color='red')
        ax3.bar(x + width/2, temp_quality[:, 1], width, label='Humidity', alpha=0.8, color='blue')
        
        ax3.set_title('Data Completeness by Device')
        ax3.set_ylabel('Completeness (%)')
        ax3.set_xlabel('Device')
        ax3.set_xticks(x)
        ax3.set_xticklabels(device_names, rotation=45, ha='right')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        ax3.axhline(y=95, color='green', linestyle='--', alpha=0.7, label='95% threshold')
    
    # 4. Daily statistics summary
    ax4 = axes[1, 1]
    
    # Calculate daily statistics
    daily_stats = df.groupby('date').agg({
        'device_name': 'nunique',  # Number of active devices
        'Temperature': ['count', 'mean', 'std'],
        'Humidity': ['count', 'mean', 'std']
    })
    
    daily_stats.columns = ['active_devices', 'temp_count', 'temp_mean', 'temp_std', 
                          'hum_count', 'hum_mean', 'hum_std']
    
    # Plot active devices over time
    ax4_twin = ax4.twinx()
    
    ax4.plot(daily_stats.index, daily_stats['temp_count'], color='red', alpha=0.7, 
            linewidth=2, label='Temperature Records')
    ax4.plot(daily_stats.index, daily_stats['hum_count'], color='blue', alpha=0.7, 
            linewidth=2, label='Humidity Records')
    
    ax4_twin.plot(daily_stats.index, daily_stats['active_devices'], color='green', 
                 linewidth=2, marker='o', markersize=3, label='Active Devices')
    
    ax4.set_title('Daily Data Statistics')
    ax4.set_ylabel('Number of Records', color='black')
    ax4_twin.set_ylabel('Active Devices', color='green')
    ax4.set_xlabel('Date')
    ax4.grid(True, alpha=0.3)
    
    # Combine legends
    lines1, labels1 = ax4.get_legend_handles_labels()
    lines2, labels2 = ax4_twin.get_legend_handles_labels()
    ax4.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
    
    plt.tight_layout()
    
    if save_plots:
        plt.savefig('data_quality_assessment.png', dpi=300, bbox_inches='tight')
        print("✓ Data quality assessment saved as 'data_quality_assessment.png'")
    
    plt.show()

def create_statistical_summary_report(df, time_col):
    """
    Create a comprehensive statistical summary report.
    
    Parameters:
    df (pd.DataFrame): The sensor data
    time_col (str): Name of the time column
    """
    
    print("\n" + "="*80)
    print("COMPREHENSIVE STATISTICAL SUMMARY REPORT")
    print("="*80)
    
    # Overall dataset statistics
    print(f"\n1. DATASET OVERVIEW:")
    print(f"   Period: {df[time_col].min().date()} to {df[time_col].max().date()}")
    print(f"   Duration: {(df[time_col].max() - df[time_col].min()).days} days")
    print(f"   Total records: {len(df):,}")
    print(f"   Unique devices: {df['device_name'].nunique()}")
    
    # Device-level statistics
    print(f"\n2. DEVICE-LEVEL ANALYSIS:")
    
    device_stats = []
    for device in sorted(df['device_name'].unique()):
        device_data = df[df['device_name'] == device]
        
        temp_data = device_data.dropna(subset=['Temperature'])
        hum_data = device_data.dropna(subset=['Humidity'])
        
        stats = {
            'device': device,
            'total_records': len(device_data),
            'temp_records': len(temp_data),
            'hum_records': len(hum_data),
            'temp_completeness': (len(temp_data) / len(device_data) * 100) if len(device_data) > 0 else 0,
            'hum_completeness': (len(hum_data) / len(device_data) * 100) if len(device_data) > 0 else 0,
            'temp_mean': temp_data['Temperature'].mean() if len(temp_data) > 0 else None,
            'temp_std': temp_data['Temperature'].std() if len(temp_data) > 0 else None,
            'hum_mean': hum_data['Humidity'].mean() if len(hum_data) > 0 else None,
            'hum_std': hum_data['Humidity'].std() if len(hum_data) > 0 else None,
            'first_reading': device_data[time_col].min(),
            'last_reading': device_data[time_col].max(),
            'days_active': (device_data[time_col].max() - device_data[time_col].min()).days + 1
        }
        device_stats.append(stats)
    
    # Sort by data quality
    device_stats.sort(key=lambda x: (x['temp_completeness'] + x['hum_completeness'])/2, reverse=True)
    
    print(f"\n   TOP 10 DEVICES BY DATA QUALITY:")
    for i, stats in enumerate(device_stats[:10], 1):
        print(f"   {i:2d}. {stats['device']:12s} - Records: {stats['total_records']:5,d}, "
              f"Temp: {stats['temp_completeness']:5.1f}%, Hum: {stats['hum_completeness']:5.1f}%, "
              f"Days: {stats['days_active']:3d}")
        if stats['temp_mean'] is not None and stats['hum_mean'] is not None:
            print(f"       Avg Temp: {stats['temp_mean']:6.1f}°C (±{stats['temp_std']:4.1f}), "
                  f"Avg Hum: {stats['hum_mean']:5.1f}% (±{stats['hum_std']:4.1f})")
    
    # Overall data quality metrics
    print(f"\n3. DATA QUALITY METRICS:")
    
    excellent_devices = [s for s in device_stats if s['temp_completeness'] >= 95 and s['hum_completeness'] >= 95]
    good_devices = [s for s in device_stats if s['temp_completeness'] >= 90 and s['hum_completeness'] >= 90 and s not in excellent_devices]
    
    print(f"   Excellent devices (≥95% complete): {len(excellent_devices)}/{len(device_stats)}")
    print(f"   Good devices (≥90% complete): {len(good_devices)}/{len(device_stats)}")
    print(f"   Average data completeness: {np.mean([s['temp_completeness'] for s in device_stats]):.1f}% (temp), "
          f"{np.mean([s['hum_completeness'] for s in device_stats]):.1f}% (hum)")
    
    # Environmental data analysis
    print(f"\n4. ENVIRONMENTAL DATA ANALYSIS:")
    
    all_temp = df.dropna(subset=['Temperature'])['Temperature']
    all_hum = df.dropna(subset=['Humidity'])['Humidity']
    
    print(f"   Temperature range: {all_temp.min():.1f}°C to {all_temp.max():.1f}°C")
    print(f"   Temperature average: {all_temp.mean():.1f}°C (±{all_temp.std():.1f}°C)")
    print(f"   Humidity range: {all_hum.min():.1f}% to {all_hum.max():.1f}%")
    print(f"   Humidity average: {all_hum.mean():.1f}% (±{all_hum.std():.1f}%)")
    
    # Correlation analysis
    temp_hum_corr = df[['Temperature', 'Humidity']].corr().iloc[0, 1]
    print(f"   Temperature-Humidity correlation: {temp_hum_corr:.3f}")
    
    # Spatial analysis readiness assessment
    print(f"\n5. SPATIAL ANALYSIS READINESS:")
    
    recommended_devices = [s['device'] for s in excellent_devices]
    
    print(f"   ✅ HIGHLY RECOMMENDED devices for spatial analysis: {len(recommended_devices)}")
    print(f"      {', '.join(recommended_devices[:10])}{'...' if len(recommended_devices) > 10 else ''}")
    
    if len(good_devices) > 0:
        print(f"   ⚠️  ACCEPTABLE devices (consider if needed): {len(good_devices)}")
        print(f"      {', '.join([s['device'] for s in good_devices[:5]])}{'...' if len(good_devices) > 5 else ''}")
    
    print(f"\n   📊 SUMMARY FOR SPATIAL ANALYSIS:")
    print(f"      • Use {len(recommended_devices)} highly recommended devices")
    print(f"      • Expected data points: ~{len(df[df['device_name'].isin(recommended_devices)]):,} records")
    print(f"      • Data quality: Excellent (>95% complete)")
    print(f"      • Temporal coverage: {(df[time_col].max() - df[time_col].min()).days} days")
    print(f"      • Environmental range: {all_temp.min():.1f}-{all_temp.max():.1f}°C, {all_hum.min():.1f}-{all_hum.max():.1f}% RH")
    
    return device_stats, recommended_devices

def main_visualization_workflow(csv_file_path):
    """
    Main workflow for comprehensive sensor data visualization.
    
    Parameters:
    csv_file_path (str): Path to the spatial analysis CSV file
    """
    
    print("="*80)
    print("COMPREHENSIVE SENSOR DATA VISUALIZATION AND VALIDATION")
    print("="*80)
    
    # Load data
    df, time_col = load_and_prepare_spatial_data(csv_file_path)
    
    # Create individual sensor plots
    create_individual_sensor_plots(df, time_col, save_plots=True)
    
    # Create multi-sensor temperature analysis
    create_multi_sensor_comparison(df, time_col, metric='Temperature', save_plots=True)
    
    # Create multi-sensor humidity analysis
    create_multi_sensor_comparison(df, time_col, metric='Humidity', save_plots=True)
    
    # Create data quality visualizations
    create_data_quality_visualization(df, time_col, save_plots=True)
    
    # Generate comprehensive statistical report
    device_stats, recommended_devices = create_statistical_summary_report(df, time_col)
    
    print(f"\n{'='*80}")
    print("VISUALIZATION AND VALIDATION COMPLETE")
    print(f"{'='*80}")
    
    return df, device_stats, recommended_devices

# ============================================================================
# INTERACTIVE VISUALIZATION FUNCTIONS (OPTIONAL)
# ============================================================================

def create_interactive_sensor_selector(df, time_col):
    """
    Create an interactive plot where users can select which sensors to display.
    
    Parameters:
    df (pd.DataFrame): The sensor data
    time_col (str): Name of the time column
    """
    
    devices = sorted(df['device_name'].unique())
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
    fig.suptitle('Interactive Multi-Sensor Visualization', fontsize=14, fontweight='bold')
    
    # Initial plot with all devices (temperature)
    lines_temp = {}
    lines_hum = {}
    
    colors = plt.cm.tab20(np.linspace(0, 1, len(devices)))
    
    for i, device in enumerate(devices):
        device_data = df[df['device_name'] == device].dropna(subset=['Temperature', 'Humidity'])
        if len(device_data) > 0:
            line_temp, = ax1.plot(device_data[time_col], device_data['Temperature'], 
                                 color=colors[i], alpha=0.7, linewidth=1.5, label=device)
            line_hum, = ax2.plot(device_data[time_col], device_data['Humidity'], 
                                color=colors[i], alpha=0.7, linewidth=1.5, label=device)
            
            lines_temp[device] = line_temp
            lines_hum[device] = line_hum
    
    ax1.set_title('Temperature (°C)')
    ax1.set_ylabel('Temperature (°C)')
    ax1.grid(True, alpha=0.3)
    
    ax2.set_title('Humidity (%)')
    ax2.set_ylabel('Humidity (%)')
    ax2.set_xlabel('Date')
    ax2.grid(True, alpha=0.3)
    
    # Format x-axis
    for ax in [ax1, ax2]:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=1))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    plt.tight_layout()
    plt.show()
    
    print("Interactive plot created. All sensors are initially visible.")
    print("Note: For full interactivity, consider using plotly or bokeh libraries.")

# ============================================================================
# USAGE EXAMPLE
# ============================================================================

if __name__ == "__main__":
    # Path to your spatial analysis data file
    csv_file_path = "Data/filtered_devices_iot_sensor_data_2025-06-06_corrected_time_10_19_March.csv"
    
    # Run the comprehensive visualization workflow
    df, device_stats, recommended_devices = main_visualization_workflow(csv_file_path)
    
    print(f"\n🎯 FINAL RECOMMENDATIONS:")
    print(f"   Use these {len(recommended_devices)} devices for spatial analysis:")
    print(f"   {', '.join(recommended_devices)}")
    
    # Optional: Create interactive visualization
    print(f"\nCreating interactive visualization...")
    create_interactive_sensor_selector(df, 'time_converted' if 'time_converted' in df.columns else 'cleaned_time')
