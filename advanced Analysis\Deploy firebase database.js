function importSheetToFirebaseUnsecure() {
  const sourceSpreadsheetId = '1rPyFWENAdBOh-tRhOk97OAOgYvMmddbfx0taQz98Yyk';
  const firebaseDatabaseUrl = "https://ulaval-iot-default-rtdb.firebaseio.com"; // Fix your URL - should end with .firebaseio.com
  
  try {
    // Step 1: Clear the database first
    Logger.log("Clearing Firebase database...");
    // clearFirebaseDatabase(firebaseDatabaseUrl);
    
    // Step 2: Get data from sheet
    const sheet = SpreadsheetApp.openById(sourceSpreadsheetId).getActiveSheet();
    const range = sheet.getDataRange();
    const values = range.getValues();
    
    if (values.length === 0) {
      Logger.log("No data found in sheet");
      return;
    }
    
    // Assuming the first row is headers
    const headers = values[0];
    Logger.log("Headers found: " + headers.join(", "));
    
    // Limit to first 1000 records for testing (plus header row)
    // const maxRows = Math.min(1001, values.length); // 1000 data rows + 1 header row
    const dataRows = values.slice(1); // Skip headers, process all rows //values.slice(1, maxRows); // Skip headers, limit rows
    
    Logger.log(`Processing ${dataRows.length} rows out of ${values.length - 1} total rows`);
    
    let successCount = 0;
    let errorCount = 0;
    
    // Step 3: Process each row
    dataRows.forEach(function(row, index) {
      try {
        const cleanedData = processRowData(row, headers);
        
        // Skip rows with critical missing data
        if (!cleanedData || !cleanedData.device_id || !cleanedData.time) {
          Logger.log(`Skipping row ${index + 2}: Missing critical data - device_id: ${cleanedData?.device_id}, time: ${cleanedData?.time}`);
          errorCount++;
          return;
        }


        // Check if record already exists in Firebase
        const existingData = getExistingData(cleanedData.device_id, cleanedData.time, firebaseDatabaseUrl);
        if (existingData) {
          Logger.log(`Skipping row ${index + 2}: Record already exists - device_id: ${cleanedData.device_id}, time: ${cleanedData.time}`);
          errorCount++;
          return;
        }


        
        // Import to Firebase
        const success = importRowToFirebase(cleanedData, firebaseDatabaseUrl);
        if (success) {
          successCount++;
        } else {
          errorCount++;
        }
        
        // Add small delay every 50 requests to avoid rate limiting
        if ((index + 1) % 50 === 0) {
          Utilities.sleep(300); // 1 second pause
          Logger.log(`Processed ${index + 1} rows...`);
        }
        
      } catch (e) {
        Logger.log(`Error processing row ${index + 2}: ${e.toString()}`);
        errorCount++;
      }
    });
    
    Logger.log(`Import completed. Success: ${successCount}, Errors: ${errorCount}`);
    Logger.log("**REMEMBER TO SECURE YOUR DATABASE RULES!**");
    
  } catch (e) {
    Logger.log(`Fatal error: ${e.toString()}`);
  }
}

// function clearFirebaseDatabase(firebaseDatabaseUrl) {
//   const clearUrl = `${firebaseDatabaseUrl}/readings.json`;
  
//   const options = {
//     'method': 'delete',
//     'muteHttpExceptions': true
//   };
  
//   try {
//     const response = UrlFetchApp.fetch(clearUrl, options);
//     const responseCode = response.getResponseCode();
    
//     if (responseCode >= 200 && responseCode < 300) {
//       Logger.log('Database cleared successfully');
//     } else {
//       Logger.log(`Warning: Could not clear database (Code: ${responseCode}): ${response.getContentText()}`);
//     }
//   } catch (e) {
//     Logger.log(`Error clearing database: ${e.toString()}`);
//   }
// }

function processRowData(row, headers) {
  const rowData = {};
  
  // Map headers to row data
  headers.forEach(function(header, index) {
    let value = row[index];
    
    // Clean and validate data
    if (value === null || value === undefined || value === '') {
      value = '';
    } else if (typeof value === 'string') {
      value = value.trim();
    }
    
    rowData[header] = value;
  });
  
  // Additional data cleaning and validation
  const cleanedData = {
    device_id: cleanRowValue(rowData.device_id || rowData['device_id']),
    time: cleanRowValue(rowData.time || rowData['time']),
    bat_status: cleanNumericValue(rowData.Bat_status || rowData['Bat_status']),
    humidity: cleanNumericValue(rowData.Humidity || rowData['Humidity']),
    prob_temperature: cleanNumericValue(rowData.Prob_temperature || rowData['Prob_temperature']),
    temperature: cleanNumericValue(rowData.Temperature || rowData['Temperature']),
    device_name: cleanRowValue(rowData.device_name || rowData['device_name']),
    timestamp: new Date().getTime(), // Add current timestamp
    import_date: new Date().toISOString() // Add import date for tracking
  };
  
  // Convert time to ISO format if it's a Date object
  if (cleanedData.time && typeof cleanedData.time === 'object' && cleanedData.time.getTime) {
    cleanedData.time = cleanedData.time.toISOString();
  }
  
  return cleanedData;
}

function cleanRowValue(value) {
  if (value === null || value === undefined) {
    return '';
  }
  
  if (typeof value === 'string') {
    return value.trim();
  }
  
  return String(value);
}

function cleanNumericValue(value) {
  if (value === null || value === undefined || value === '') {
    return null;
  }
  
  if (typeof value === 'number') {
    return isNaN(value) ? null : value;
  }
  
  if (typeof value === 'string') {
    const trimmed = value.trim();
    if (trimmed === '') return null;
    
    const parsed = parseFloat(trimmed);
    return isNaN(parsed) ? null : parsed;
  }
  
  return null;
}


function getExistingData(deviceId, time, firebaseDatabaseUrl) {
  const url = `${firebaseDatabaseUrl}/readings/${deviceId}/${time}.json`;
  
  const options = {
    'muteHttpExceptions': true
  };
  
  try {
    const response = UrlFetchApp.fetch(url, options);
    const responseCode = response.getResponseCode();
    
    if (responseCode === 200) {
      return JSON.parse(response.getContentText());
    } else {
      return null;
    }
  } catch (e) {
    Logger.log(`Error checking existing data: ${e.toString()}`);
    return null;
  }
}



function importRowToFirebase(rowData, firebaseDatabaseUrl) {
  // Create a more organized structure: /readings/{device_id}/{timestamp}
  const deviceId = rowData.device_id.replace(/[.#$[\]]/g, '_'); // Firebase key sanitization
  const timestamp = new Date().getTime() + Math.random() * 1000; // Ensure uniqueness
  
  const url = `${firebaseDatabaseUrl}/readings/${deviceId}/${Math.floor(timestamp)}.json`;
  
  const options = {
    'method': 'put', // Use PUT for specific path instead of POST
    'payload': JSON.stringify(rowData),
    'contentType': 'application/json',
    'muteHttpExceptions': true
  };
  
  try {
    const response = UrlFetchApp.fetch(url, options);
    const responseCode = response.getResponseCode();
    const responseBody = response.getContentText();
    
    if (responseCode >= 200 && responseCode < 300) {
      return true;
    } else {
      Logger.log(`Error importing row for device ${rowData.device_id} (Code: ${responseCode}): ${responseBody}`);
      return false;
    }
    
  } catch (e) {
    Logger.log(`Fetch error for device ${rowData.device_id}: ${e.toString()}`);
    return false;
  }
}

