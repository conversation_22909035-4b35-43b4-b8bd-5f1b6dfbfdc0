// Option 1: Using HTTP Request Node (Recommended)
// Replace the Google Sheets node with this function node

// Function node: "Prepare Firebase Data"
var firebaseUrl = "https://YOUR_PROJECT_ID-default-rtdb.firebaseio.com/";
var databasePath = "sensor_data"; // Your database path

// Parse the CSV data back to object format
const parsedData = msg.payload.split(","); 
const data = {
    'device_id': parsedData[0] || "",
    'time': parsedData[1] || "",
    'Bat_status': parsedData[2] || "",
    'Humidity': parsedData[3] || "",
    'Prob_temperature': parsedData[4] || "",
    'Temperature': parsedData[5] || ""
};

// Add device name mapping (from your existing function)
const deviceMapping = {
    "eui-a840412631838a0d": "LHT65_Raja-0",
    "eui-a8404155818613ff": "LHT65N-11",
    // ... (keep all your existing device mappings)
};

data['device_name'] = deviceMapping[data['device_id']] || "";

// Create timestamp-based key for Firebase
const timestamp = new Date().getTime();
const deviceId = data['device_id'].replace(/[.#$[\]]/g, '_'); // Firebase key sanitization

// Prepare for Firebase
msg.url = `${firebaseUrl}${databasePath}/${deviceId}/${timestamp}.json`;
msg.method = "POST";
msg.payload = data;
msg.headers = {
    "Content-Type": "application/json"
};

return msg;

// =============================================================
// Option 2: Using node-red-contrib-firebase-realtime-database
// Install: npm install node-red-contrib-firebase-realtime-database
// =============================================================

// If you choose to use the Firebase node instead of HTTP requests,
// you'll need to:
// 1. Install the firebase node: npm install node-red-contrib-firebase-realtime-database
// 2. Configure it with your Firebase credentials
// 3. Replace the Google Sheets node with Firebase Realtime Database node

// Function node: "Format for Firebase Node"
const parsedData = msg.payload.split(","); 
const data = {
    device_id: parsedData[0] || "",
    time: parsedData[1] || "",
    bat_status: parsedData[2] || "",
    humidity: parsedData[3] || "",
    prob_temperature: parsedData[4] || "",
    temperature: parsedData[5] || "",
    timestamp: new Date().getTime()
};

// Add device name
const deviceMapping = {
    // ... your device mappings
};
data.device_name = deviceMapping[data.device_id] || "";

// Set the path for Firebase
const deviceId = data.device_id.replace(/[.#$[\]]/g, '_');
msg.firebase_path = `sensor_data/${deviceId}`;
msg.payload = data;

return msg;

// =============================================================
// Firebase Security Rules (set in Firebase Console)
// =============================================================
/*
{
  "rules": {
    "sensor_data": {
      ".read": "auth != null",
      ".write": "auth != null",
      "$device_id": {
        ".validate": "newData.hasChildren(['device_id', 'time', 'timestamp'])"
      }
    }
  }
}

// For testing (less secure):
{
  "rules": {
    ".read": true,
    ".write": true
  }
}
*/

// =============================================================
// Database Structure in Firebase will look like:
// =============================================================
/*
sensor_data/
├── eui-a840412631838a0d/
│   ├── 1703123456789: {
│   │   device_id: "eui-a840412631838a0d",
│   │   device_name: "LHT65_Raja-0",
│   │   time: "2024-01-01 10:30:45",
│   │   bat_status: "3.6",
│   │   humidity: "65.2",
│   │   prob_temperature: "22.1",
│   │   temperature: "21.8",
│   │   timestamp: 1703123456789
│   │   }
│   └── 1703123556889: { ... }
└── eui-a8404155818613ff/
    └── 1703123456790: { ... }
*/