
function onEditBackup() {
  
  const allowedDeviceNames = ['LSN50-1', "LSN50-2", 'LSN50-3', "LSN50-4", "LSN50-5", 'LSN50-6', 'LSN50-7', "LSN50-8", 'LSN50-9', "LSN50-10",  "LHT65N-7",  "LHT65N-9","LHT65N-10",  "LHT65N-12",  "LHT65N-13",  "LHT65N-15", "LHT65N-18","LHT65N-19",  "LHT65N-20","LHT65N-21", "LHT65N-23", "LHT65N-24", "LHT65N-25", "LHT65N-26", "LHT65N-27", "LHT65N-28","LHT65N-31", "LHT65N-32", "LHT65N-33","LHT65N-34","LHT65N-35", ]; // Add the desired device names
  
  const sourceSpreadsheet = SpreadsheetApp.openById('1rPyFWENAdBOh-tRhOk97OAOgYvMmddbfx0taQz98Yyk');
  const sourceSheet = sourceSpreadsheet.getSheetByName('Sheet1');
  

  
  const backupSpreadsheet = SpreadsheetApp.openById('1xwJOkIZRJe-21gVjqaTJHa9WUBUIL6cs4o4VMTMBxRI');
  const backupSheet = backupSpreadsheet.getSheetByName('Sheet1');
  

  
  const sourceData = sourceSheet.getDataRange().getValues(); // Includes header row
  const sourceHeaders = sourceData[0]; // Assume the first row contains headers
  const sourceRows = sourceData.slice(1); // Data excluding the header

  
  const backupData = backupSheet.getDataRange().getValues();
  const backupRows = backupData.slice(1); // Data excluding the header
  const lastBackupRow = backupSheet.getLastRow();
  var lastBackupTime = new Date(backupSheet.getRange(lastBackupRow, 2).getValues()[0][0])

  console.log(lastBackupTime)
  const newRows = sourceRows.filter(row => {
    const deviceName = row[sourceHeaders.indexOf('device_name')];
    const recordDate = new Date(row[sourceHeaders.indexOf('time')]);
    // console.log(recordDate>lastBackupTime)
    
    return allowedDeviceNames.includes(deviceName) &&  (recordDate  > lastBackupTime)
    
  });
// console.log(newRows)
  // Append the filtered rows to the backup sheet
  if (newRows.length > 0) {
    const startRow = backupSheet.getLastRow() + 1;
    backupSheet.getRange(startRow, 1, newRows.length, newRows[0].length).setValues(newRows);
  }
}
