import csv
import json
import re
from datetime import datetime
from typing import Dict, Any, Optional
# import firebase_admin
# from firebase_admin import credentials, db

class SensorDataImporter:
    def __init__(self, csv_file_path: str = "sensor_data.csv"):
        self.csv_file_path = csv_file_path
        self.processed_records = []
        self.invalid_records = []
        
    def sanitize_device_id(self, device_id: str) -> str:
        """Sanitize device_id for use as Firebase key"""
        # Replace Firebase invalid characters with underscores
        sanitized = re.sub(r'[.#$\[\]/]', '_', device_id)
        return sanitized
    
    def parse_timestamp(self, time_str: str) -> Optional[int]:
        """Convert time string to Unix timestamp in milliseconds"""
        try:
            # Remove timezone info and parse
            # Format: "Wed Nov 27 2024 09:32:26 GMT-0500 (Eastern Standard Time)"
            # Extract the main part: "Wed Nov 27 2024 09:32:26"
            time_part = time_str.split(' GMT')[0]
            
            # Parse the datetime
            dt = datetime.strptime(time_part, "%a %b %d %Y %H:%M:%S")
            
            # Convert to Unix timestamp in milliseconds
            unix_timestamp = int(dt.timestamp() * 1000)
            return unix_timestamp
        except Exception as e:
            print(f"Error parsing timestamp '{time_str}': {e}")
            return None
    
    def convert_to_float(self, value: str) -> Optional[float]:
        """Convert string to float, return None for empty/invalid values"""
        if not value or value.strip() == "" or value.strip() == '""':
            return None
        try:
            return float(value.strip())
        except (ValueError, TypeError):
            return None
    
    def is_valid_record(self, row: Dict[str, Any]) -> bool:
        """Check if record has required data for Bat_status, Temperature, and Humidity"""
        required_fields = ['Bat_status', 'Temperature', 'Humidity']
        
        for field in required_fields:
            value = row.get(field)
            if value is None:
                return False
        
        return True
    
    def process_csv_row(self, row: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """Process a single CSV row into cleaned JSON format"""
        # Parse timestamp
        unix_timestamp = self.parse_timestamp(row['time'])
        if unix_timestamp is None:
            return None
        
        # Clean and convert numeric fields
        processed_row = {
            'device_id': row['device_id'],
            'time': unix_timestamp,
            'Bat_status': self.convert_to_float(row['Bat_status']),
            'Humidity': self.convert_to_float(row['Humidity']),
            'Prob_temperature': self.convert_to_float(row['Prob_temperature']),
            'Temperature': self.convert_to_float(row['Temperature']),
            'device_name': row['device_name'].strip() if row['device_name'] else None
        }
        
        # Check if record is valid (has required sensor data)
        if not self.is_valid_record(processed_row):
            return None
            
        return processed_row
    
    def read_and_process_csv(self):
        """Read CSV file and process all rows"""
        try:
            with open(self.csv_file_path, 'r', newline='', encoding='utf-8') as csvfile:
                # Strip whitespace from headers
                reader = csv.DictReader(csvfile)
                reader.fieldnames = [field.strip() for field in reader.fieldnames]
                
                for row_num, row in enumerate(reader, start=2):  # Start at 2 for header
                    # Strip whitespace from all values
                    cleaned_row = {k: v.strip() if isinstance(v, str) else v for k, v in row.items()}
                    
                    processed_row = self.process_csv_row(cleaned_row)
                    
                    if processed_row:
                        self.processed_records.append(processed_row)
                    else:
                        self.invalid_records.append({
                            'row_number': row_num,
                            'original_data': cleaned_row
                        })
                        
        except FileNotFoundError:
            print(f"Error: CSV file '{self.csv_file_path}' not found.")
            return False
        except Exception as e:
            print(f"Error reading CSV file: {e}")
            return False
            
        return True
    
    def structure_for_firebase(self) -> Dict[str, Any]:
        """Structure processed data for Firebase Realtime Database"""
        firebase_data = {"readings": {}}
        
        for record in self.processed_records:
            # Sanitize device_id for Firebase key
            sanitized_device_id = self.sanitize_device_id(record['device_id'])
            unix_timestamp = str(record['time'])  # Firebase keys should be strings
            
            # Create the nested structure
            if sanitized_device_id not in firebase_data["readings"]:
                firebase_data["readings"][sanitized_device_id] = {}
            
            firebase_data["readings"][sanitized_device_id][unix_timestamp] = record
            
        return firebase_data
    
    def initialize_firebase(self, credentials_path: str, database_url: str):
        """Initialize Firebase Admin SDK"""
        try:
            cred = credentials.Certificate(credentials_path)
            firebase_admin.initialize_app(cred, {
                'databaseURL': database_url
            })
            return True
        except Exception as e:
            print(f"Error initializing Firebase: {e}")
            return False
    
    def upload_to_firebase(self, firebase_data: Dict[str, Any], batch_size: int = 100):
        """Upload data to Firebase Realtime Database in batches"""
        try:
            ref = db.reference('/')
            
            # Get all device readings
            readings = firebase_data["readings"]
            
            for device_id, device_readings in readings.items():
                print(f"Uploading data for device: {device_id}")
                
                # Upload in batches to avoid overwhelming Firebase
                timestamps = list(device_readings.keys())
                for i in range(0, len(timestamps), batch_size):
                    batch_timestamps = timestamps[i:i + batch_size]
                    batch_data = {ts: device_readings[ts] for ts in batch_timestamps}
                    
                    # Upload batch
                    device_ref = ref.child('readings').child(device_id)
                    device_ref.update(batch_data)
                    
                    print(f"  Uploaded batch {i//batch_size + 1} ({len(batch_data)} records)")
            
            print("Upload completed successfully!")
            return True
            
        except Exception as e:
            print(f"Error uploading to Firebase: {e}")
            return False
    
    def save_to_json(self, firebase_data: Dict[str, Any], output_file: str = "firebase_data.json"):
        """Save structured data to JSON file for inspection"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(firebase_data, f, indent=2, ensure_ascii=False)
            print(f"Data saved to {output_file}")
            return True
        except Exception as e:
            print(f"Error saving JSON file: {e}")
            return False
    
    def print_summary(self):
        """Print processing summary"""
        print("\n" + "="*50)
        print("PROCESSING SUMMARY")
        print("="*50)
        print(f"Valid records processed: {len(self.processed_records)}")
        print(f"Invalid records skipped: {len(self.invalid_records)}")
        
        if self.invalid_records:
            print("\nInvalid records (missing required sensor data):")
            for invalid in self.invalid_records[:5]:  # Show first 5
                print(f"  Row {invalid['row_number']}: {invalid['original_data']}")
            if len(self.invalid_records) > 5:
                print(f"  ... and {len(self.invalid_records) - 5} more")
        
        # Show device distribution
        device_counts = {}
        for record in self.processed_records:
            device_id = record['device_id']
            device_counts[device_id] = device_counts.get(device_id, 0) + 1
        
        print(f"\nData distribution across {len(device_counts)} devices:")
        for device_id, count in sorted(device_counts.items()):
            sanitized = self.sanitize_device_id(device_id)
            print(f"  {device_id} -> {sanitized}: {count} records")


def main():
    # Configuration
    CSV_FILE = "sensor_data.csv"
    # FIREBASE_CREDENTIALS = "path/to/your/firebase-credentials.json"  # Update this path
    # DATABASE_URL = "https://your-project-id-default-rtdb.firebaseio.com/"  # Update this URL
    
    # Initialize importer
    importer = SensorDataImporter(CSV_FILE)
    
    # Process CSV file
    print("Reading and processing CSV file...")
    if not importer.read_and_process_csv():
        return
    
    


    
    # Print summary
    importer.print_summary()
    
    # Structure data for Firebase
    print("\nStructuring data for Firebase...")
    firebase_data = importer.structure_for_firebase()
    
    # Save to JSON file for inspection (optional)
    importer.save_to_json(firebase_data, "firebase_structured_data.json")
    
    
    
    
    # # Upload to Firebase (uncomment when ready)
    # upload_choice = input("\nDo you want to upload to Firebase now? (y/n): ").lower().strip()
    
    # if upload_choice == 'y':
    #     print("Initializing Firebase...")
    #     if importer.initialize_firebase(FIREBASE_CREDENTIALS, DATABASE_URL):
    #         print("Uploading to Firebase...")
    #         importer.upload_to_firebase(firebase_data)
    #     else:
    #         print("Failed to initialize Firebase. Check your credentials and database URL.")
    # else:
    #     print("Data processing completed. Check the JSON file to verify the structure.")
    #     print("Update the Firebase credentials and database URL, then run again to upload.")


if __name__ == "__main__":
    main()