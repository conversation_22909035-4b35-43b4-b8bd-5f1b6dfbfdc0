"""
Comprehensive IoT Sensor Data Analysis for Laval University Campus
Scientific Paper Analysis Script

This script performs extensive data analysis on IoT sensor data deployed across
Laval University campus in Quebec City for scientific research purposes.

Study Location: Laval University Campus, Quebec City
Coordinates: 46.782835771899464, -71.27038736335324
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from datetime import datetime, timedelta
import warnings
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import DBSCAN
from sklearn.decomposition import PCA
import requests
import json
from typing import Dict, List, Tuple
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.offline as pyo

warnings.filterwarnings('ignore')

# Set scientific plotting style
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")

class SensorDataAnalyzer:
    """
    Comprehensive analyzer for IoT sensor data with scientific research focus
    """
    
    def __init__(self, filepath: str):
        """Initialize analyzer with data file path"""
        self.filepath = filepath
        self.df = None
        self.processed_df = None
        self.analysis_results = {}
        
        # Study area coordinates (Laval University)
        self.study_lat = 46.782835771899464
        self.study_lon = -71.27038736335324
        
    def load_and_preprocess_data(self):
        """Load and preprocess the sensor data"""
        print("Loading and preprocessing sensor data...")
        
        # Load data
        self.df = pd.read_csv(self.filepath)
        
        # Convert timestamp
        self.df['timestamp'] = pd.to_datetime(self.df['cleaned_time'])
        
        # Clean numeric data
        self.df['Temperature'] = pd.to_numeric(self.df['Temperature'], errors='coerce')
        self.df['Humidity'] = pd.to_numeric(self.df['Humidity'], errors='coerce')
        
        # Remove outliers using IQR method
        self.processed_df = self.df.copy()
        
        # Calculate basic statistics
        temp_q1 = self.processed_df['Temperature'].quantile(0.25)
        temp_q3 = self.processed_df['Temperature'].quantile(0.75)
        temp_iqr = temp_q3 - temp_q1
        temp_lower = temp_q1 - 1.5 * temp_iqr
        temp_upper = temp_q3 + 1.5 * temp_iqr
        
        hum_q1 = self.processed_df['Humidity'].quantile(0.25)
        hum_q3 = self.processed_df['Humidity'].quantile(0.75)
        hum_iqr = hum_q3 - hum_q1
        hum_lower = hum_q1 - 1.5 * hum_iqr
        hum_upper = hum_q3 + 1.5 * hum_iqr
        
        # Filter outliers
        self.processed_df = self.processed_df[
            (self.processed_df['Temperature'] >= temp_lower) &
            (self.processed_df['Temperature'] <= temp_upper) &
            (self.processed_df['Humidity'] >= hum_lower) &
            (self.processed_df['Humidity'] <= hum_upper)
        ]
        
        print(f"Data loaded: {len(self.df)} total records")
        print(f"After preprocessing: {len(self.processed_df)} records")
        print(f"Unique sensors: {self.df['device_name'].nunique()}")
        
    def basic_statistical_analysis(self):
        """Perform comprehensive statistical analysis"""
        print("\n=== BASIC STATISTICAL ANALYSIS ===")
        
        # Overall statistics
        stats_summary = {
            'Temperature': {
                'count': self.processed_df['Temperature'].count(),
                'mean': self.processed_df['Temperature'].mean(),
                'std': self.processed_df['Temperature'].std(),
                'min': self.processed_df['Temperature'].min(),
                'max': self.processed_df['Temperature'].max(),
                'median': self.processed_df['Temperature'].median(),
                'range': self.processed_df['Temperature'].max() - self.processed_df['Temperature'].min()
            },
            'Humidity': {
                'count': self.processed_df['Humidity'].count(),
                'mean': self.processed_df['Humidity'].mean(),
                'std': self.processed_df['Humidity'].std(),
                'min': self.processed_df['Humidity'].min(),
                'max': self.processed_df['Humidity'].max(),
                'median': self.processed_df['Humidity'].median(),
                'range': self.processed_df['Humidity'].max() - self.processed_df['Humidity'].min()
            }
        }
        
        # Per-sensor statistics
        sensor_stats = self.processed_df.groupby('device_name').agg({
            'Temperature': ['count', 'mean', 'std', 'min', 'max', 'median'],
            'Humidity': ['count', 'mean', 'std', 'min', 'max', 'median']
        }).round(3)
        
        self.analysis_results['overall_stats'] = stats_summary
        self.analysis_results['sensor_stats'] = sensor_stats
        
        # Print summary
        print("Overall Temperature Statistics:")
        for key, value in stats_summary['Temperature'].items():
            print(f"  {key}: {value:.3f}")
            
        print("\nOverall Humidity Statistics:")
        for key, value in stats_summary['Humidity'].items():
            print(f"  {key}: {value:.3f}")
            
        return stats_summary, sensor_stats
    
    def plot_statistical_overview(self):
        """Create comprehensive statistical overview plots"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Statistical Overview of IoT Sensor Network\nLaval University Campus', 
                     fontsize=16, fontweight='bold')
        
        # Temperature distribution
        axes[0,0].hist(self.processed_df['Temperature'], bins=50, alpha=0.7, color='red')
        axes[0,0].axvline(self.processed_df['Temperature'].mean(), color='black', 
                         linestyle='--', label=f'Mean: {self.processed_df["Temperature"].mean():.2f}°C')
        axes[0,0].set_xlabel('Temperature (°C)')
        axes[0,0].set_ylabel('Frequency')
        axes[0,0].set_title('Temperature Distribution')
        axes[0,0].legend()
        axes[0,0].grid(True, alpha=0.3)
        
        # Humidity distribution
        axes[0,1].hist(self.processed_df['Humidity'], bins=50, alpha=0.7, color='blue')
        axes[0,1].axvline(self.processed_df['Humidity'].mean(), color='black', 
                         linestyle='--', label=f'Mean: {self.processed_df["Humidity"].mean():.2f}%')
        axes[0,1].set_xlabel('Humidity (%)')
        axes[0,1].set_ylabel('Frequency')
        axes[0,1].set_title('Humidity Distribution')
        axes[0,1].legend()
        axes[0,1].grid(True, alpha=0.3)
        
        # Box plots for temperature by sensor
        sensor_temp_data = [group['Temperature'].dropna() for name, group in self.processed_df.groupby('device_name')]
        axes[0,2].boxplot(sensor_temp_data, labels=self.processed_df['device_name'].unique())
        axes[0,2].set_xlabel('Sensor')
        axes[0,2].set_ylabel('Temperature (°C)')
        axes[0,2].set_title('Temperature Distribution by Sensor')
        axes[0,2].tick_params(axis='x', rotation=45)
        
        # Box plots for humidity by sensor
        sensor_hum_data = [group['Humidity'].dropna() for name, group in self.processed_df.groupby('device_name')]
        axes[1,0].boxplot(sensor_hum_data, labels=self.processed_df['device_name'].unique())
        axes[1,0].set_xlabel('Sensor')
        axes[1,0].set_ylabel('Humidity (%)')
        axes[1,0].set_title('Humidity Distribution by Sensor')
        axes[1,0].tick_params(axis='x', rotation=45)
        
        # Time series overview
        hourly_avg = self.processed_df.set_index('timestamp').resample('1H').agg({
            'Temperature': 'mean',
            'Humidity': 'mean'
        })
        
        ax1 = axes[1,1]
        ax2 = ax1.twinx()
        
        line1 = ax1.plot(hourly_avg.index, hourly_avg['Temperature'], 'r-', label='Temperature')
        line2 = ax2.plot(hourly_avg.index, hourly_avg['Humidity'], 'b-', label='Humidity')
        
        ax1.set_xlabel('Time')
        ax1.set_ylabel('Temperature (°C)', color='red')
        ax2.set_ylabel('Humidity (%)', color='blue')
        ax1.set_title('Temporal Overview')
        
        # Combine legends
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax1.legend(lines, labels, loc='upper left')
        
        # Correlation heatmap
        corr_data = self.processed_df[['Temperature', 'Humidity']].corr()
        im = axes[1,2].imshow(corr_data, cmap='RdBu_r', aspect='auto')
        axes[1,2].set_xticks(range(len(corr_data.columns)))
        axes[1,2].set_yticks(range(len(corr_data.columns)))
        axes[1,2].set_xticklabels(corr_data.columns)
        axes[1,2].set_yticklabels(corr_data.columns)
        axes[1,2].set_title('Temperature-Humidity Correlation')
        
        # Add correlation values
        for i in range(len(corr_data.columns)):
            for j in range(len(corr_data.columns)):
                axes[1,2].text(j, i, f'{corr_data.iloc[i, j]:.3f}', 
                              ha='center', va='center', fontweight='bold')
        
        plt.colorbar(im, ax=axes[1,2])
        plt.tight_layout()
        plt.savefig('01_statistical_overview.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def sensor_variation_analysis(self):
        """Analyze sensor variation patterns"""
        print("\n=== SENSOR VARIATION ANALYSIS ===")
        
        # Calculate variation metrics for each sensor
        sensor_variation = self.processed_df.groupby('device_name').agg({
            'Temperature': ['std', 'var', lambda x: x.max() - x.min()],
            'Humidity': ['std', 'var', lambda x: x.max() - x.min()]
        }).round(3)
        
        # Flatten column names
        sensor_variation.columns = ['_'.join(col).strip() for col in sensor_variation.columns]
        
        # Find sensors with maximum and minimum variation
        temp_max_var_sensor = sensor_variation['Temperature_std'].idxmax()
        temp_min_var_sensor = sensor_variation['Temperature_std'].idxmin()
        hum_max_var_sensor = sensor_variation['Humidity_std'].idxmax()
        hum_min_var_sensor = sensor_variation['Humidity_std'].idxmin()
        
        self.analysis_results['variation_analysis'] = {
            'sensor_variation': sensor_variation,
            'temp_max_var': temp_max_var_sensor,
            'temp_min_var': temp_min_var_sensor,
            'hum_max_var': hum_max_var_sensor,
            'hum_min_var': hum_min_var_sensor
        }
        
        print(f"Temperature - Maximum variation sensor: {temp_max_var_sensor}")
        print(f"Temperature - Minimum variation sensor: {temp_min_var_sensor}")
        print(f"Humidity - Maximum variation sensor: {hum_max_var_sensor}")
        print(f"Humidity - Minimum variation sensor: {hum_min_var_sensor}")
        
        return sensor_variation
    
    def plot_variation_analysis(self):
        """Plot sensor variation analysis"""
        sensor_variation = self.analysis_results['variation_analysis']['sensor_variation']
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Sensor Variation Analysis\nLaval University Campus IoT Network', 
                     fontsize=16, fontweight='bold')
        
        # Temperature standard deviation
        axes[0,0].bar(range(len(sensor_variation)), sensor_variation['Temperature_std'])
        axes[0,0].set_xlabel('Sensor Index')
        axes[0,0].set_ylabel('Temperature Standard Deviation (°C)')
        axes[0,0].set_title('Temperature Variation by Sensor')
        axes[0,0].tick_params(axis='x', rotation=45)
        
        # Highlight max and min variation sensors
        max_idx = sensor_variation['Temperature_std'].idxmax()
        min_idx = sensor_variation['Temperature_std'].idxmin()
        max_pos = list(sensor_variation.index).index(max_idx)
        min_pos = list(sensor_variation.index).index(min_idx)
        
        axes[0,0].bar(max_pos, sensor_variation.loc[max_idx, 'Temperature_std'], 
                     color='red', label=f'Max: {max_idx}')
        axes[0,0].bar(min_pos, sensor_variation.loc[min_idx, 'Temperature_std'], 
                     color='green', label=f'Min: {min_idx}')
        axes[0,0].legend()
        
        # Humidity standard deviation
        axes[0,1].bar(range(len(sensor_variation)), sensor_variation['Humidity_std'])
        axes[0,1].set_xlabel('Sensor Index')
        axes[0,1].set_ylabel('Humidity Standard Deviation (%)')
        axes[0,1].set_title('Humidity Variation by Sensor')
        axes[0,1].tick_params(axis='x', rotation=45)
        
        # Highlight max and min variation sensors
        max_idx_hum = sensor_variation['Humidity_std'].idxmax()
        min_idx_hum = sensor_variation['Humidity_std'].idxmin()
        max_pos_hum = list(sensor_variation.index).index(max_idx_hum)
        min_pos_hum = list(sensor_variation.index).index(min_idx_hum)
        
        axes[0,1].bar(max_pos_hum, sensor_variation.loc[max_idx_hum, 'Humidity_std'], 
                     color='red', label=f'Max: {max_idx_hum}')
        axes[0,1].bar(min_pos_hum, sensor_variation.loc[min_idx_hum, 'Humidity_std'], 
                     color='green', label=f'Min: {min_idx_hum}')
        axes[0,1].legend()
        
        # Time series for maximum variation sensors
        max_var_temp_data = self.processed_df[self.processed_df['device_name'] == max_idx]
        min_var_temp_data = self.processed_df[self.processed_df['device_name'] == min_idx]
        
        axes[1,0].plot(max_var_temp_data['timestamp'], max_var_temp_data['Temperature'], 
                      'r-', label=f'{max_idx} (High Variation)', alpha=0.7)
        axes[1,0].plot(min_var_temp_data['timestamp'], min_var_temp_data['Temperature'], 
                      'g-', label=f'{min_idx} (Low Variation)', alpha=0.7)
        axes[1,0].set_xlabel('Time')
        axes[1,0].set_ylabel('Temperature (°C)')
        axes[1,0].set_title('Temperature: High vs Low Variation Sensors')
        axes[1,0].legend()
        axes[1,0].grid(True, alpha=0.3)
        
        # Time series for humidity variation sensors
        max_var_hum_data = self.processed_df[self.processed_df['device_name'] == max_idx_hum]
        min_var_hum_data = self.processed_df[self.processed_df['device_name'] == min_idx_hum]
        
        axes[1,1].plot(max_var_hum_data['timestamp'], max_var_hum_data['Humidity'], 
                      'r-', label=f'{max_idx_hum} (High Variation)', alpha=0.7)
        axes[1,1].plot(min_var_hum_data['timestamp'], min_var_hum_data['Humidity'], 
                      'g-', label=f'{min_idx_hum} (Low Variation)', alpha=0.7)
        axes[1,1].set_xlabel('Time')
        axes[1,1].set_ylabel('Humidity (%)')
        axes[1,1].set_title('Humidity: High vs Low Variation Sensors')
        axes[1,1].legend()
        axes[1,1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('02_variation_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def reliability_and_outlier_analysis(self):
        """Identify unreliable sensors and outliers"""
        print("\n=== RELIABILITY AND OUTLIER ANALYSIS ===")
        
        # Calculate reliability metrics
        sensor_reliability = {}
        
        for sensor in self.processed_df['device_name'].unique():
            sensor_data = self.processed_df[self.processed_df['device_name'] == sensor]
            
            # Data availability
            total_possible_readings = len(self.processed_df['timestamp'].unique())
            actual_readings = len(sensor_data)
            availability_ratio = actual_readings / total_possible_readings
            
            # Temperature outliers using Z-score
            temp_zscore = np.abs(stats.zscore(sensor_data['Temperature'].dropna()))
            temp_outliers = np.sum(temp_zscore > 3)
            
            # Humidity outliers using Z-score
            hum_zscore = np.abs(stats.zscore(sensor_data['Humidity'].dropna()))
            hum_outliers = np.sum(hum_zscore > 3)
            
            # Calculate measurement consistency (coefficient of variation)
            temp_cv = sensor_data['Temperature'].std() / abs(sensor_data['Temperature'].mean())
            hum_cv = sensor_data['Humidity'].std() / abs(sensor_data['Humidity'].mean())
            
            sensor_reliability[sensor] = {
                'availability_ratio': availability_ratio,
                'temp_outliers': temp_outliers,
                'hum_outliers': hum_outliers,
                'temp_cv': temp_cv,
                'hum_cv': hum_cv,
                'total_readings': actual_readings
            }
        
        # Convert to DataFrame for easier analysis
        reliability_df = pd.DataFrame(sensor_reliability).T
        
        # Identify problematic sensors
        low_availability = reliability_df[reliability_df['availability_ratio'] < 0.5].index.tolist()
        high_temp_outliers = reliability_df[reliability_df['temp_outliers'] > 5].index.tolist()
        high_hum_outliers = reliability_df[reliability_df['hum_outliers'] > 5].index.tolist()
        high_variation = reliability_df[
            (reliability_df['temp_cv'] > reliability_df['temp_cv'].quantile(0.9)) |
            (reliability_df['hum_cv'] > reliability_df['hum_cv'].quantile(0.9))
        ].index.tolist()
        
        self.analysis_results['reliability'] = {
            'reliability_df': reliability_df,
            'low_availability': low_availability,
            'high_temp_outliers': high_temp_outliers,
            'high_hum_outliers': high_hum_outliers,
            'high_variation': high_variation
        }
        
        print(f"Sensors with low availability (< 50%): {low_availability}")
        print(f"Sensors with high temperature outliers (> 5): {high_temp_outliers}")
        print(f"Sensors with high humidity outliers (> 5): {high_hum_outliers}")
        print(f"Sensors with high variation: {high_variation}")
        
        return reliability_df
    
    def plot_reliability_analysis(self):
        """Plot reliability and outlier analysis"""
        reliability_df = self.analysis_results['reliability']['reliability_df']
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Sensor Reliability and Outlier Analysis\nLaval University Campus', 
                     fontsize=16, fontweight='bold')
        
        # Data availability
        axes[0,0].bar(range(len(reliability_df)), reliability_df['availability_ratio'])
        axes[0,0].axhline(y=0.5, color='red', linestyle='--', label='50% threshold')
        axes[0,0].set_xlabel('Sensor Index')
        axes[0,0].set_ylabel('Data Availability Ratio')
        axes[0,0].set_title('Data Availability by Sensor')
        axes[0,0].legend()
        axes[0,0].tick_params(axis='x', rotation=45)
        
        # Temperature outliers
        axes[0,1].bar(range(len(reliability_df)), reliability_df['temp_outliers'])
        axes[0,1].axhline(y=5, color='red', linestyle='--', label='5 outliers threshold')
        axes[0,1].set_xlabel('Sensor Index')
        axes[0,1].set_ylabel('Number of Temperature Outliers')
        axes[0,1].set_title('Temperature Outliers by Sensor')
        axes[0,1].legend()
        axes[0,1].tick_params(axis='x', rotation=45)
        
        # Humidity outliers
        axes[0,2].bar(range(len(reliability_df)), reliability_df['hum_outliers'])
        axes[0,2].axhline(y=5, color='red', linestyle='--', label='5 outliers threshold')
        axes[0,2].set_xlabel('Sensor Index')
        axes[0,2].set_ylabel('Number of Humidity Outliers')
        axes[0,2].set_title('Humidity Outliers by Sensor')
        axes[0,2].legend()
        axes[0,2].tick_params(axis='x', rotation=45)
        
        # Coefficient of variation - Temperature
        axes[1,0].bar(range(len(reliability_df)), reliability_df['temp_cv'])
        axes[1,0].axhline(y=reliability_df['temp_cv'].quantile(0.9), color='red', 
                         linestyle='--', label='90th percentile')
        axes[1,0].set_xlabel('Sensor Index')
        axes[1,0].set_ylabel('Temperature Coefficient of Variation')
        axes[1,0].set_title('Temperature Measurement Consistency')
        axes[1,0].legend()
        axes[1,0].tick_params(axis='x', rotation=45)
        
        # Coefficient of variation - Humidity
        axes[1,1].bar(range(len(reliability_df)), reliability_df['hum_cv'])
        axes[1,1].axhline(y=reliability_df['hum_cv'].quantile(0.9), color='red', 
                         linestyle='--', label='90th percentile')
        axes[1,1].set_xlabel('Sensor Index')
        axes[1,1].set_ylabel('Humidity Coefficient of Variation')
        axes[1,1].set_title('Humidity Measurement Consistency')
        axes[1,1].legend()
        axes[1,1].tick_params(axis='x', rotation=45)
        
        # Overall reliability score
        # Composite score: availability * (1 - normalized_outliers) * (1 - normalized_cv)
        temp_outlier_norm = reliability_df['temp_outliers'] / reliability_df['temp_outliers'].max()
        hum_outlier_norm = reliability_df['hum_outliers'] / reliability_df['hum_outliers'].max()
        temp_cv_norm = reliability_df['temp_cv'] / reliability_df['temp_cv'].max()
        hum_cv_norm = reliability_df['hum_cv'] / reliability_df['hum_cv'].max()
        
        reliability_score = (reliability_df['availability_ratio'] * 
                           (1 - (temp_outlier_norm + hum_outlier_norm) / 2) * 
                           (1 - (temp_cv_norm + hum_cv_norm) / 2))
        
        bars = axes[1,2].bar(range(len(reliability_df)), reliability_score)
        axes[1,2].set_xlabel('Sensor Index')
        axes[1,2].set_ylabel('Composite Reliability Score')
        axes[1,2].set_title('Overall Sensor Reliability Ranking')
        axes[1,2].tick_params(axis='x', rotation=45)
        
        # Color bars based on reliability
        for i, bar in enumerate(bars):
            if reliability_score.iloc[i] > 0.7:
                bar.set_color('green')
            elif reliability_score.iloc[i] > 0.5:
                bar.set_color('orange')
            else:
                bar.set_color('red')
        
        plt.tight_layout()
        plt.savefig('03_reliability_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def temporal_analysis(self):
        """Analyze temporal patterns in sensor data"""
        print("\n=== TEMPORAL ANALYSIS ===")
        
        # Add time components
        self.processed_df['hour'] = self.processed_df['timestamp'].dt.hour
        self.processed_df['day'] = self.processed_df['timestamp'].dt.day
        self.processed_df['minute'] = self.processed_df['timestamp'].dt.minute
        
        # Hourly patterns
        hourly_stats = self.processed_df.groupby('hour').agg({
            'Temperature': ['mean', 'std', 'min', 'max'],
            'Humidity': ['mean', 'std', 'min', 'max']
        })
        
        # Daily patterns  
        daily_stats = self.processed_df.groupby('day').agg({
            'Temperature': ['mean', 'std', 'min', 'max'],
            'Humidity': ['mean', 'std', 'min', 'max']
        })
        
        self.analysis_results['temporal'] = {
            'hourly_stats': hourly_stats,
            'daily_stats': daily_stats
        }
        
        return hourly_stats, daily_stats
    
    def plot_temporal_analysis(self):
        """Plot temporal analysis"""
        hourly_stats = self.analysis_results['temporal']['hourly_stats']
        daily_stats = self.analysis_results['temporal']['daily_stats']
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Temporal Analysis of IoT Sensor Data\nLaval University Campus', 
                     fontsize=16, fontweight='bold')
        
        # Hourly temperature patterns
        axes[0,0].plot(hourly_stats.index, hourly_stats[('Temperature', 'mean')], 'ro-', label='Mean')
        axes[0,0].fill_between(hourly_stats.index, 
                              hourly_stats[('Temperature', 'mean')] - hourly_stats[('Temperature', 'std')],
                              hourly_stats[('Temperature', 'mean')] + hourly_stats[('Temperature', 'std')],
                              alpha=0.3, label='±1 Std Dev')
        axes[0,0].set_xlabel('Hour of Day')
        axes[0,0].set_ylabel('Temperature (°C)')
        axes[0,0].set_title('Hourly Temperature Patterns')
        axes[0,0].legend()
        axes[0,0].grid(True, alpha=0.3)
        
        # Hourly humidity patterns
        axes[0,1].plot(hourly_stats.index, hourly_stats[('Humidity', 'mean')], 'bo-', label='Mean')
        axes[0,1].fill_between(hourly_stats.index, 
                              hourly_stats[('Humidity', 'mean')] - hourly_stats[('Humidity', 'std')],
                              hourly_stats[('Humidity', 'mean')] + hourly_stats[('Humidity', 'std')],
                              alpha=0.3, label='±1 Std Dev')
        axes[0,1].set_xlabel('Hour of Day')
        axes[0,1].set_ylabel('Humidity (%)')
        axes[0,1].set_title('Hourly Humidity Patterns')
        axes[0,1].legend()
        axes[0,1].grid(True, alpha=0.3)
        
        # Daily temperature patterns
        axes[1,0].plot(daily_stats.index, daily_stats[('Temperature', 'mean')], 'ro-', label='Mean')
        axes[1,0].fill_between(daily_stats.index, 
                              daily_stats[('Temperature', 'mean')] - daily_stats[('Temperature', 'std')],
                              daily_stats[('Temperature', 'mean')] + daily_stats[('Temperature', 'std')],
                              alpha=0.3, label='±1 Std Dev')
        axes[1,0].set_xlabel('Day of Month')
        axes[1,0].set_ylabel('Temperature (°C)')
        axes[1,0].set_title('Daily Temperature Patterns')
        axes[1,0].legend()
        axes[1,0].grid(True, alpha=0.3)
        
        # Daily humidity patterns
        axes[1,1].plot(daily_stats.index, daily_stats[('Humidity', 'mean')], 'bo-', label='Mean')
        axes[1,1].fill_between(daily_stats.index, 
                              daily_stats[('Humidity', 'mean')] - daily_stats[('Humidity', 'std')],
                              daily_stats[('Humidity', 'mean')] + daily_stats[('Humidity', 'std')],
                              alpha=0.3, label='±1 Std Dev')
        axes[1,1].set_xlabel('Day of Month')
        axes[1,1].set_ylabel('Humidity (%)')
        axes[1,1].set_title('Daily Humidity Patterns')
        axes[1,1].legend()
        axes[1,1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('04_temporal_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def spatial_clustering_analysis(self):
        """Perform spatial clustering analysis for future land cover analysis"""
        print("\n=== SPATIAL CLUSTERING ANALYSIS ===")
        
        # Since we don't have exact coordinates, we'll use sensor characteristics for clustering
        # Prepare features for clustering
        sensor_features = self.processed_df.groupby('device_name').agg({
            'Temperature': ['mean', 'std', 'min', 'max'],
            'Humidity': ['mean', 'std', 'min', 'max']
        })
        
        # Flatten column names
        sensor_features.columns = ['_'.join(col).strip() for col in sensor_features.columns]
        
        # Standardize features
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(sensor_features)
        
        # Perform DBSCAN clustering
        dbscan = DBSCAN(eps=0.5, min_samples=2)
        clusters = dbscan.fit_predict(features_scaled)
        
        # Add cluster labels
        sensor_features['cluster'] = clusters
        
        # PCA for visualization
        pca = PCA(n_components=2)
        features_pca = pca.fit_transform(features_scaled)
        
        self.analysis_results['clustering'] = {
            'sensor_features': sensor_features,
            'features_pca': features_pca,
            'clusters': clusters,
            'pca_explained_variance': pca.explained_variance_ratio_
        }
        
        print(f"Number of clusters identified: {len(set(clusters)) - (1 if -1 in clusters else 0)}")
        print(f"Number of noise points: {list(clusters).count(-1)}")
        print(f"PCA explained variance ratio: {pca.explained_variance_ratio_}")
        
        return sensor_features, features_pca, clusters
    
    def plot_clustering_analysis(self):
        """Plot clustering analysis"""
        sensor_features = self.analysis_results['clustering']['sensor_features']
        features_pca = self.analysis_results['clustering']['features_pca']
        clusters = self.analysis_results['clustering']['clusters']
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Spatial Clustering Analysis for Land Cover Classification\nLaval University Campus', 
                     fontsize=16, fontweight='bold')
        
        # PCA scatter plot
        unique_clusters = set(clusters)
        colors = plt.cm.Set1(np.linspace(0, 1, len(unique_clusters)))
        
        for cluster, color in zip(unique_clusters, colors):
            mask = clusters == cluster
            label = f'Cluster {cluster}' if cluster != -1 else 'Noise'
            axes[0,0].scatter(features_pca[mask, 0], features_pca[mask, 1], 
                             c=[color], label=label, s=100, alpha=0.7)
        
        axes[0,0].set_xlabel(f'PC1 ({self.analysis_results["clustering"]["pca_explained_variance"][0]:.2%} variance)')
        axes[0,0].set_ylabel(f'PC2 ({self.analysis_results["clustering"]["pca_explained_variance"][1]:.2%} variance)')
        axes[0,0].set_title('PCA-based Sensor Clustering')
        axes[0,0].legend()
        axes[0,0].grid(True, alpha=0.3)
        
        # Temperature vs Humidity by cluster
        for cluster, color in zip(unique_clusters, colors):
            mask = clusters == cluster
            cluster_data = sensor_features[sensor_features['cluster'] == cluster]
            label = f'Cluster {cluster}' if cluster != -1 else 'Noise'
            axes[0,1].scatter(cluster_data['Temperature_mean'], cluster_data['Humidity_mean'], 
                             c=[color], label=label, s=100, alpha=0.7)
        
        axes[0,1].set_xlabel('Mean Temperature (°C)')
        axes[0,1].set_ylabel('Mean Humidity (%)')
        axes[0,1].set_title('Temperature-Humidity Clustering')
        axes[0,1].legend()
        axes[0,1].grid(True, alpha=0.3)
        
        # Cluster characteristics - Temperature
        cluster_temp_means = []
        cluster_labels = []
        for cluster in unique_clusters:
            if cluster != -1:  # Exclude noise
                cluster_data = sensor_features[sensor_features['cluster'] == cluster]
                cluster_temp_means.append(cluster_data['Temperature_mean'].mean())
                cluster_labels.append(f'Cluster {cluster}')
        
        axes[1,0].bar(cluster_labels, cluster_temp_means, alpha=0.7)
        axes[1,0].set_xlabel('Cluster')
        axes[1,0].set_ylabel('Mean Temperature (°C)')
        axes[1,0].set_title('Average Temperature by Cluster')
        axes[1,0].tick_params(axis='x', rotation=45)
        
        # Cluster characteristics - Humidity
        cluster_hum_means = []
        for cluster in unique_clusters:
            if cluster != -1:  # Exclude noise
                cluster_data = sensor_features[sensor_features['cluster'] == cluster]
                cluster_hum_means.append(cluster_data['Humidity_mean'].mean())
        
        axes[1,1].bar(cluster_labels, cluster_hum_means, alpha=0.7)
        axes[1,1].set_xlabel('Cluster')
        axes[1,1].set_ylabel('Mean Humidity (%)')
        axes[1,1].set_title('Average Humidity by Cluster')
        axes[1,1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig('05_clustering_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def generate_comprehensive_report(self):
        """Generate comprehensive scientific report"""
        print("\n" + "="*60)
        print("COMPREHENSIVE SCIENTIFIC ANALYSIS REPORT")
        print("IoT Sensor Network - Laval University Campus")
        print("="*60)
        
        # Basic statistics
        print("\n1. DATASET OVERVIEW:")
        print(f"   - Total sensors: {self.df['device_name'].nunique()}")
        print(f"   - Total measurements: {len(self.df):,}")
        print(f"   - Study period: {self.processed_df['timestamp'].min()} to {self.processed_df['timestamp'].max()}")
        print(f"   - Data quality: {len(self.processed_df)/len(self.df)*100:.1f}% after preprocessing")
        
        # Temperature analysis
        temp_stats = self.analysis_results['overall_stats']['Temperature']
        print(f"\n2. TEMPERATURE ANALYSIS:")
        print(f"   - Mean: {temp_stats['mean']:.2f}°C")
        print(f"   - Standard deviation: {temp_stats['std']:.2f}°C")
        print(f"   - Range: {temp_stats['min']:.2f}°C to {temp_stats['max']:.2f}°C")
        print(f"   - Median: {temp_stats['median']:.2f}°C")
        
        # Humidity analysis
        hum_stats = self.analysis_results['overall_stats']['Humidity']
        print(f"\n3. HUMIDITY ANALYSIS:")
        print(f"   - Mean: {hum_stats['mean']:.2f}%")
        print(f"   - Standard deviation: {hum_stats['std']:.2f}%")
        print(f"   - Range: {hum_stats['min']:.2f}% to {hum_stats['max']:.2f}%")
        print(f"   - Median: {hum_stats['median']:.2f}%")
        
        # Variation analysis
        var_analysis = self.analysis_results['variation_analysis']
        print(f"\n4. SENSOR VARIATION ANALYSIS:")
        print(f"   - Highest temperature variation: {var_analysis['temp_max_var']}")
        print(f"   - Lowest temperature variation: {var_analysis['temp_min_var']}")
        print(f"   - Highest humidity variation: {var_analysis['hum_max_var']}")
        print(f"   - Lowest humidity variation: {var_analysis['hum_min_var']}")
        
        # Reliability analysis
        reliability = self.analysis_results['reliability']
        print(f"\n5. RELIABILITY ASSESSMENT:")
        print(f"   - Sensors with low availability: {len(reliability['low_availability'])}")
        print(f"   - Sensors with high temperature outliers: {len(reliability['high_temp_outliers'])}")
        print(f"   - Sensors with high humidity outliers: {len(reliability['high_hum_outliers'])}")
        print(f"   - Sensors with high variation: {len(reliability['high_variation'])}")
        
        # Clustering results
        clustering = self.analysis_results['clustering']
        n_clusters = len(set(clustering['clusters'])) - (1 if -1 in clustering['clusters'] else 0)
        print(f"\n6. SPATIAL CLUSTERING (for future land cover analysis):")
        print(f"   - Number of sensor clusters identified: {n_clusters}")
        print(f"   - PCA variance explained: {clustering['pca_explained_variance'][0]:.1%} + {clustering['pca_explained_variance'][1]:.1%}")
        
        print(f"\n7. RECOMMENDATIONS FOR SCIENTIFIC STUDY:")
        print(f"   - Focus on sensors with high reliability scores for critical measurements")
        print(f"   - Investigate sensors with unusual patterns for potential environmental factors")
        print(f"   - Use clustering results to guide land cover classification analysis")
        print(f"   - Consider temporal patterns for diurnal cycle analysis")
        
        # Save detailed results to CSV
        detailed_results = pd.DataFrame({
            'sensor': self.analysis_results['sensor_stats'].index,
        })
        
        # Add statistics
        for metric in ['mean', 'std', 'min', 'max']:
            detailed_results[f'temp_{metric}'] = self.analysis_results['sensor_stats'][('Temperature', metric)].values
            detailed_results[f'hum_{metric}'] = self.analysis_results['sensor_stats'][('Humidity', metric)].values
        
        # Add reliability metrics
        reliability_df = self.analysis_results['reliability']['reliability_df']
        detailed_results = detailed_results.merge(reliability_df, left_on='sensor', right_index=True, how='left')
        
        # Add cluster information
        clustering_df = self.analysis_results['clustering']['sensor_features'][['cluster']]
        detailed_results = detailed_results.merge(clustering_df, left_on='sensor', right_index=True, how='left')
        
        detailed_results.to_csv('sensor_analysis_detailed_results.csv', index=False)
        print(f"\n   - Detailed results saved to 'sensor_analysis_detailed_results.csv'")
        
        print("\n" + "="*60)
    
    def create_weather_comparison_app(self):
        """Create simple web application for weather comparison"""
        print("\n=== WEATHER COMPARISON APPLICATION ===")
        
        # Calculate our sensor averages
        our_temp_avg = self.processed_df['Temperature'].mean()
        our_hum_avg = self.processed_df['Humidity'].mean()
        
        # Create HTML for web application
        html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laval University IoT vs Public Weather Data</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }}
        h1 {{
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }}
        .comparison-grid {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }}
        .data-card {{
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
        }}
        .public-card {{
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }}
        .metric {{
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }}
        .label {{
            font-size: 1.2em;
            opacity: 0.9;
        }}
        .info {{
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }}
        .coordinates {{
            color: #666;
            font-style: italic;
        }}
        #weatherData {{
            margin-top: 20px;
            padding: 20px;
            background: #e8f5e8;
            border-radius: 10px;
        }}
        button {{
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }}
        button:hover {{
            background: #2980b9;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>Weather Data Comparison</h1>
        <h2>Laval University Campus IoT Network vs Public Weather Stations</h2>
        
        <div class="comparison-grid">
            <div class="data-card">
                <div class="metric">{our_temp_avg:.1f}°C</div>
                <div class="label">IoT Network Average Temperature</div>
                <div style="margin-top: 15px;">
                    <div style="font-size: 1.5em;">{our_hum_avg:.1f}%</div>
                    <div style="font-size: 0.9em;">Average Humidity</div>
                </div>
            </div>
            
            <div class="data-card public-card">
                <div class="metric" id="publicTemp">--°C</div>
                <div class="label">Public Weather Station</div>
                <div style="margin-top: 15px;">
                    <div style="font-size: 1.5em;" id="publicHum">--%</div>
                    <div style="font-size: 0.9em;">Humidity</div>
                </div>
            </div>
        </div>
        
        <div class="info">
            <h3>Study Location</h3>
            <p class="coordinates">Latitude: {self.study_lat}, Longitude: {self.study_lon}</p>
            <p>Laval University Campus, Quebec City, Canada</p>
            
            <button onclick="fetchWeatherData()">Fetch Current Weather Data</button>
            <button onclick="compareData()">Compare with IoT Data</button>
            
            <div id="weatherData"></div>
            <div id="comparison"></div>
        </div>
    </div>

    <script>
        async function fetchWeatherData() {{
            const lat = {self.study_lat};
            const lon = {self.study_lon};
            
            // Using OpenWeatherMap API (requires API key)
            // For demonstration, we'll show mock data
            const mockWeatherData = {{
                temperature: -5.2,
                humidity: 78,
                description: "Overcast clouds",
                station: "Quebec City Airport"
            }};
            
            document.getElementById('publicTemp').textContent = mockWeatherData.temperature + '°C';
            document.getElementById('publicHum').textContent = mockWeatherData.humidity + '%';
            
            document.getElementById('weatherData').innerHTML = `
                <h4>Latest Public Weather Data</h4>
                <p><strong>Station:</strong> ${{mockWeatherData.station}}</p>
                <p><strong>Conditions:</strong> ${{mockWeatherData.description}}</p>
                <p><strong>Temperature:</strong> ${{mockWeatherData.temperature}}°C</p>
                <p><strong>Humidity:</strong> ${{mockWeatherData.humidity}}%</p>
                <p><em>Note: This is mock data. For real implementation, integrate with OpenWeatherMap API.</em></p>
            `;
        }}
        
        function compareData() {{
            const iotTemp = {our_temp_avg:.1f};
            const iotHum = {our_hum_avg:.1f};
            const publicTemp = -5.2; // Mock data
            const publicHum = 78; // Mock data
            
            const tempDiff = iotTemp - publicTemp;
            const humDiff = iotHum - publicHum;
            
            document.getElementById('comparison').innerHTML = `
                <h4>Comparison Analysis</h4>
                <p><strong>Temperature Difference:</strong> ${{tempDiff > 0 ? '+' : ''}}${{tempDiff.toFixed(1)}}°C</p>
                <p><strong>Humidity Difference:</strong> ${{humDiff > 0 ? '+' : ''}}${{humDiff.toFixed(1)}}%</p>
                <p><strong>Analysis:</strong> ${{Math.abs(tempDiff) < 2 ? 'Good agreement' : 'Significant difference'}} in temperature measurements.</p>
                <p><em>Differences may be due to microclimate effects, sensor placement, or measurement timing.</em></p>
            `;
        }}
    </script>
</body>
</html>
        """
        
        # Save HTML file
        with open('weather_comparison_app.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print("Weather comparison web application created: 'weather_comparison_app.html'")
        print("Open this file in a web browser to use the application.")
        print(f"Our IoT network averages: Temperature {our_temp_avg:.1f}°C, Humidity {our_hum_avg:.1f}%")
    
    def run_complete_analysis(self):
        """Run the complete analysis pipeline"""
        print("Starting comprehensive IoT sensor data analysis...")
        print("Study location: Laval University Campus, Quebec City")
        print(f"Coordinates: {self.study_lat}, {self.study_lon}")
        
        # Load and preprocess data
        self.load_and_preprocess_data()
        
        # Run all analyses
        self.basic_statistical_analysis()
        self.plot_statistical_overview()
        
        self.sensor_variation_analysis()
        self.plot_variation_analysis()
        
        self.reliability_and_outlier_analysis()
        self.plot_reliability_analysis()
        
        self.temporal_analysis()
        self.plot_temporal_analysis()
        
        self.spatial_clustering_analysis()
        self.plot_clustering_analysis()
        
        # Generate comprehensive report
        self.generate_comprehensive_report()
        
        # Create weather comparison app
        self.create_weather_comparison_app()
        
        self.export_for_web_app()
        
        print("\n" + "="*60)
        print("ANALYSIS COMPLETE!")
        print("Generated files:")
        print("- 01_statistical_overview.png")
        print("- 02_variation_analysis.png") 
        print("- 03_reliability_analysis.png")
        print("- 04_temporal_analysis.png")
        print("- 05_clustering_analysis.png")
        print("- sensor_analysis_detailed_results.csv")
        print("- weather_comparison_app.html")
        print("="*60)

    def export_for_web_app(self):
        """Export processed sensor data in format compatible with web application"""
        print("\n=== EXPORTING DATA FOR WEB APPLICATION ===")
        
        # Option 1: Export time series data (if you want detailed comparison)
        web_app_data = self.processed_df[['timestamp', 'Temperature', 'Humidity']].copy()
        
        # Clean and format the data
        web_app_data = web_app_data.dropna()
        
        # Sort by timestamp
        web_app_data = web_app_data.sort_values('timestamp')
        
        # Option A: Hourly averages (recommended for cleaner visualization)
        hourly_data = web_app_data.set_index('timestamp').resample('1H').agg({
            'Temperature': 'mean',
            'Humidity': 'mean'
        }).reset_index()
        
        # Remove any remaining NaN values
        hourly_data = hourly_data.dropna()
        
        # Save hourly data
        hourly_data.to_csv('iot_sensor_data_for_web_app_hourly.csv', index=False)
        print(f"✅ Exported {len(hourly_data)} hourly averages to 'iot_sensor_data_for_web_app_hourly.csv'")
        
        # Option B: All raw data points (might be too many points for visualization)
        web_app_data.to_csv('iot_sensor_data_for_web_app_raw.csv', index=False)
        print(f"✅ Exported {len(web_app_data)} raw data points to 'iot_sensor_data_for_web_app_raw.csv'")
        
        # Option 2: Export summary statistics for manual input
        overall_stats = self.analysis_results['overall_stats']
        
        summary_stats = {
            'Start Date': self.processed_df['timestamp'].min().strftime('%Y-%m-%d'),
            'End Date': self.processed_df['timestamp'].max().strftime('%Y-%m-%d'),
            'Average Temperature (°C)': overall_stats['Temperature']['mean'],
            'Average Humidity (%)': overall_stats['Humidity']['mean'],
            'Temperature Std Dev': overall_stats['Temperature']['std'],
            'Humidity Std Dev': overall_stats['Humidity']['std'],
            'Total Measurements': len(self.processed_df),
            'Number of Sensors': self.processed_df['device_name'].nunique()
        }
        
        # Save summary for manual input
        summary_df = pd.DataFrame([summary_stats])
        summary_df.to_csv('iot_summary_for_web_app.csv', index=False)
        
        print("\n📊 SUMMARY STATISTICS FOR MANUAL INPUT:")
        print(f"   📅 Date Range: {summary_stats['Start Date']} to {summary_stats['End Date']}")
        print(f"   🌡️  Average Temperature: {summary_stats['Average Temperature (°C)']:.2f}°C")
        print(f"   💧 Average Humidity: {summary_stats['Average Humidity (%)']:.2f}%")
        print(f"   📈 Temperature Std Dev: {summary_stats['Temperature Std Dev']:.2f}°C")
        print(f"   📈 Humidity Std Dev: {summary_stats['Humidity Std Dev']:.2f}%")
        
        print(f"\n✅ Summary statistics saved to 'iot_summary_for_web_app.csv'")
        
        return hourly_data, web_app_data, summary_stats

# Example usage
if __name__ == "__main__":
    # Initialize analyzer
    analyzer = SensorDataAnalyzer('Data/filtered_devices_iot_sensor_data_2025-06-06_corrected_time_10_19_March.csv')
    
    # Run complete analysis
    analyzer.run_complete_analysis()
    
    # Additional custom analysis can be added here
    print("\nAdditional analysis recommendations:")
    print("1. Integrate GPS coordinates for each sensor for spatial analysis")
    print("2. Obtain land cover classification data for the study area")
    print("3. Set up automated comparison with Environment Canada weather stations")
    print("4. Implement real-time monitoring dashboard")
    print("5. Add statistical significance tests for sensor comparisons")
