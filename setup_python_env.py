"""
Python Environment Setup Script for VS Code
This script helps configure VS Code to use the correct Python interpreter (Anaconda/Spyder environment)
"""

import os
import json
import subprocess
import sys
from pathlib import Path

def find_anaconda_python():
    """Find Anaconda Python installation paths"""
    possible_paths = [
        r"C:\ProgramData\Anaconda3\python.exe",
        r"C:\Users\<USER>\Anaconda3\python.exe".format(os.getenv('USERNAME')),
        r"C:\Users\<USER>\AppData\Local\Continuum\anaconda3\python.exe".format(os.getenv('USERNAME')),
        r"C:\Anaconda3\python.exe",
        r"C:\ProgramData\Miniconda3\python.exe",
        r"C:\Users\<USER>\Miniconda3\python.exe".format(os.getenv('USERNAME')),
    ]
    
    found_paths = []
    for path in possible_paths:
        if os.path.exists(path):
            found_paths.append(path)
            print(f"✓ Found Python at: {path}")
    
    return found_paths

def test_python_packages(python_path):
    """Test if required packages are available in the Python environment"""
    required_packages = ['pandas', 'numpy', 'matplotlib', 'seaborn']
    
    print(f"\nTesting packages in: {python_path}")
    
    for package in required_packages:
        try:
            result = subprocess.run([python_path, '-c', f'import {package}; print(f"{package} version: {{getattr({package}, "__version__", "unknown")}}")'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"✓ {result.stdout.strip()}")
            else:
                print(f"❌ {package}: Not available")
                return False
        except Exception as e:
            print(f"❌ {package}: Error testing - {e}")
            return False
    
    return True

def create_vscode_settings(python_path):
    """Create VS Code settings to use the specified Python interpreter"""
    
    # Create .vscode directory if it doesn't exist
    vscode_dir = Path('.vscode')
    vscode_dir.mkdir(exist_ok=True)
    
    # Settings for VS Code
    settings = {
        "python.defaultInterpreterPath": python_path,
        "python.pythonPath": python_path,
        "python.terminal.activateEnvironment": True,
        "python.terminal.activateEnvInCurrentTerminal": True
    }
    
    settings_file = vscode_dir / 'settings.json'
    
    # Load existing settings if they exist
    if settings_file.exists():
        try:
            with open(settings_file, 'r') as f:
                existing_settings = json.load(f)
            existing_settings.update(settings)
            settings = existing_settings
        except:
            pass
    
    # Write settings
    with open(settings_file, 'w') as f:
        json.dump(settings, f, indent=4)
    
    print(f"✓ Created VS Code settings at: {settings_file}")
    return settings_file

def create_launch_script(python_path):
    """Create a convenient script to run Python with the correct interpreter"""
    
    script_content = f'''@echo off
REM Python launcher script using Anaconda environment
REM Usage: python_run.bat your_script.py

"{python_path}" %*
'''
    
    with open('python_run.bat', 'w') as f:
        f.write(script_content)
    
    print("✓ Created python_run.bat - Use this to run Python scripts")
    print("  Example: python_run.bat your_script.py")

def main():
    print("="*60)
    print("PYTHON ENVIRONMENT SETUP FOR VS CODE")
    print("="*60)
    
    # Find Anaconda installations
    print("\n1. Searching for Anaconda/Python installations...")
    python_paths = find_anaconda_python()
    
    if not python_paths:
        print("❌ No Anaconda Python installations found!")
        print("Please install Anaconda or Miniconda first.")
        return
    
    # Test each installation
    print("\n2. Testing Python environments...")
    working_python = None
    
    for python_path in python_paths:
        if test_python_packages(python_path):
            working_python = python_path
            print(f"✅ Found working Python environment: {python_path}")
            break
        else:
            print(f"❌ Python environment missing required packages: {python_path}")
    
    if not working_python:
        print("\n❌ No Python environment found with required packages!")
        print("Please install the required packages in your Anaconda environment:")
        print("  conda install pandas numpy matplotlib seaborn")
        return
    
    # Create VS Code configuration
    print("\n3. Configuring VS Code...")
    settings_file = create_vscode_settings(working_python)
    
    # Create launch script
    print("\n4. Creating convenience scripts...")
    create_launch_script(working_python)
    
    print("\n" + "="*60)
    print("SETUP COMPLETE!")
    print("="*60)
    print(f"✅ Python interpreter: {working_python}")
    print(f"✅ VS Code settings: {settings_file}")
    print("✅ Launch script: python_run.bat")
    
    print("\nNext steps:")
    print("1. Restart VS Code")
    print("2. Open Command Palette (Ctrl+Shift+P)")
    print("3. Type 'Python: Select Interpreter'")
    print(f"4. Select: {working_python}")
    print("\nOr simply use: python_run.bat your_script.py")

if __name__ == "__main__":
    main()
