import pandas as pd
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

def comprehensive_iot_analysis(csv_file_path):
    """
    Comprehensive analysis of IoT sensor data from CSV file.
    
    Parameters:
    csv_file_path (str): Path to the CSV file containing IoT sensor data
    
    Returns:
    dict: Dictionary containing all analysis results
    """
    
    print("="*60)
    print("COMPREHENSIVE IoT SENSOR DATA ANALYSIS")
    print("="*60)
    
    # ============================================================================
    # 1. DATA LOADING AND BASIC INFORMATION
    # ============================================================================
    
    print("\n1. LOADING DATA...")
    try:
        df = pd.read_csv(csv_file_path)
        print(f"✓ Successfully loaded {len(df):,} records")
        print(f"✓ Dataset shape: {df.shape}")
        print(f"✓ Columns: {list(df.columns)}")
    except Exception as e:
        print(f"✗ Error loading file: {e}")
        return None
    
    # ============================================================================
    # 2. DATA QUALITY ASSESSMENT
    # ============================================================================
    
    print("\n2. DATA QUALITY ASSESSMENT")
    print("-" * 40)
    
    # Missing values analysis
    missing_data = df.isnull().sum()
    total_records = len(df)
    
    print("Missing values per column:")
    for col, missing_count in missing_data.items():
        percentage = (missing_count / total_records) * 100
        print(f"  {col}: {missing_count:,} ({percentage:.2f}%)")
    
    # Data types
    print("\nData types:")
    for col, dtype in df.dtypes.items():
        print(f"  {col}: {dtype}")
    
    # ============================================================================
    # 3. DEVICE ANALYSIS
    # ============================================================================
    
    print("\n3. DEVICE ANALYSIS")
    print("-" * 40)
    
    # Unique devices
    unique_devices = df['device_name'].nunique()
    print(f"Total unique devices: {unique_devices}")
    
    # Records per device
    device_counts = df['device_name'].value_counts()
    print(f"\nRecords per device:")
    for device, count in device_counts.head(10).items():
        percentage = (count / total_records) * 100
        print(f"  {device}: {count:,} records ({percentage:.1f}%)")
    
    if len(device_counts) > 10:
        print(f"  ... and {len(device_counts) - 10} more devices")
    
    # Device type analysis (extract base type from device name)
    df['device_type'] = df['device_name'].str.extract(r'([A-Z]+\d+)')
    device_type_counts = df['device_type'].value_counts()
    
    print(f"\nDevice type distribution:")
    for device_type, count in device_type_counts.items():
        percentage = (count / total_records) * 100
        print(f"  {device_type}: {count:,} records ({percentage:.1f}%)")
    
    # ============================================================================
    # 4. TEMPERATURE ANALYSIS
    # ============================================================================
    
    print("\n4. TEMPERATURE ANALYSIS")
    print("-" * 40)
    
    # Convert temperature to numeric if it's not already
    df['Temperature'] = pd.to_numeric(df['Temperature'], errors='coerce')
    
    # Remove any remaining non-numeric values
    valid_temp = df['Temperature'].dropna()
    
    if len(valid_temp) > 0:
        temp_stats = {
            'count': len(valid_temp),
            'min': valid_temp.min(),
            'max': valid_temp.max(),
            'mean': valid_temp.mean(),
            'median': valid_temp.median(),
            'std': valid_temp.std()
        }
        
        print(f"Valid temperature readings: {temp_stats['count']:,}")
        print(f"Temperature range: {temp_stats['min']:.2f}°C to {temp_stats['max']:.2f}°C")
        print(f"Average temperature: {temp_stats['mean']:.2f}°C")
        print(f"Median temperature: {temp_stats['median']:.2f}°C")
        print(f"Standard deviation: {temp_stats['std']:.2f}°C")
        
        # Temperature distribution
        temp_ranges = {
            'Below 0°C': len(valid_temp[valid_temp < 0]),
            '0-10°C': len(valid_temp[(valid_temp >= 0) & (valid_temp < 10)]),
            '10-20°C': len(valid_temp[(valid_temp >= 10) & (valid_temp < 20)]),
            '20-30°C': len(valid_temp[(valid_temp >= 20) & (valid_temp < 30)]),
            'Above 30°C': len(valid_temp[valid_temp >= 30])
        }
        
        print(f"\nTemperature distribution:")
        for range_name, count in temp_ranges.items():
            percentage = (count / len(valid_temp)) * 100
            print(f"  {range_name}: {count:,} readings ({percentage:.1f}%)")
    else:
        print("✗ No valid temperature data found")
        temp_stats = None
    
    # ============================================================================
    # 5. HUMIDITY ANALYSIS
    # ============================================================================
    
    print("\n5. HUMIDITY ANALYSIS")
    print("-" * 40)
    
    # Convert humidity to numeric if it's not already
    df['Humidity'] = pd.to_numeric(df['Humidity'], errors='coerce')
    
    # Remove any remaining non-numeric values
    valid_humidity = df['Humidity'].dropna()
    
    if len(valid_humidity) > 0:
        humidity_stats = {
            'count': len(valid_humidity),
            'min': valid_humidity.min(),
            'max': valid_humidity.max(),
            'mean': valid_humidity.mean(),
            'median': valid_humidity.median(),
            'std': valid_humidity.std()
        }
        
        completeness = (len(valid_humidity) / total_records) * 100
        print(f"Valid humidity readings: {humidity_stats['count']:,} ({completeness:.1f}% complete)")
        print(f"Humidity range: {humidity_stats['min']:.1f}% to {humidity_stats['max']:.1f}%")
        print(f"Average humidity: {humidity_stats['mean']:.1f}%")
        print(f"Median humidity: {humidity_stats['median']:.1f}%")
        print(f"Standard deviation: {humidity_stats['std']:.1f}%")
        
        # Humidity distribution
        humidity_ranges = {
            'Very Low (0-30%)': len(valid_humidity[valid_humidity < 30]),
            'Low (30-50%)': len(valid_humidity[(valid_humidity >= 30) & (valid_humidity < 50)]),
            'Moderate (50-70%)': len(valid_humidity[(valid_humidity >= 50) & (valid_humidity < 70)]),
            'High (70-90%)': len(valid_humidity[(valid_humidity >= 70) & (valid_humidity < 90)]),
            'Very High (90-100%)': len(valid_humidity[valid_humidity >= 90])
        }
        
        print(f"\nHumidity distribution:")
        for range_name, count in humidity_ranges.items():
            percentage = (count / len(valid_humidity)) * 100
            print(f"  {range_name}: {count:,} readings ({percentage:.1f}%)")
    else:
        print("✗ No valid humidity data found")
        humidity_stats = None
    
    # ============================================================================
    # 6. TIME SERIES ANALYSIS
    # ============================================================================
    
    print("\n6. TIME SERIES ANALYSIS")
    print("-" * 40)
    
    # Convert time column to datetime
    time_col = 'time_converted' if 'time_converted' in df.columns else 'cleaned_time'
    
    try:
        df[time_col] = pd.to_datetime(df[time_col])
        df_time_valid = df.dropna(subset=[time_col])
        
        if len(df_time_valid) > 0:
            time_stats = {
                'start_date': df_time_valid[time_col].min(),
                'end_date': df_time_valid[time_col].max(),
                'duration_days': (df_time_valid[time_col].max() - df_time_valid[time_col].min()).days,
                'total_records': len(df_time_valid)
            }
            
            print(f"Data period: {time_stats['start_date'].strftime('%Y-%m-%d')} to {time_stats['end_date'].strftime('%Y-%m-%d')}")
            print(f"Duration: {time_stats['duration_days']} days")
            print(f"Average records per day: {time_stats['total_records'] / max(time_stats['duration_days'], 1):.0f}")
            
            # Monthly distribution
            df_time_valid['month_year'] = df_time_valid[time_col].dt.to_period('M')
            monthly_counts = df_time_valid['month_year'].value_counts().sort_index()
            
            print(f"\nMonthly data distribution:")
            for month, count in monthly_counts.items():
                print(f"  {month}: {count:,} records")
                
        else:
            print("✗ No valid time data found")
            time_stats = None
            
    except Exception as e:
        print(f"✗ Error processing time data: {e}")
        time_stats = None
    
    # ============================================================================
    # 7. DEVICE PERFORMANCE ANALYSIS
    # ============================================================================
    
    print("\n7. DEVICE PERFORMANCE ANALYSIS")
    print("-" * 40)
    
    # Average readings per device
    device_performance = df.groupby('device_name').agg({
        'Temperature': ['count', 'mean', 'std'],
        'Humidity': ['count', 'mean', 'std']
    }).round(2)
    
    # Flatten column names
    device_performance.columns = ['_'.join(col).strip() for col in device_performance.columns]
    
    print("Top 10 devices by record count:")
    top_devices = device_performance.sort_values('Temperature_count', ascending=False).head(10)
    
    for device in top_devices.index:
        temp_count = top_devices.loc[device, 'Temperature_count']
        temp_mean = top_devices.loc[device, 'Temperature_mean']
        hum_count = top_devices.loc[device, 'Humidity_count']
        hum_mean = top_devices.loc[device, 'Humidity_mean']
        
        print(f"  {device}:")
        print(f"    Records: {temp_count:,}")
        print(f"    Avg Temperature: {temp_mean:.1f}°C")
        print(f"    Avg Humidity: {hum_mean:.1f}%")
    
    # ============================================================================
    # 8. CORRELATION ANALYSIS
    # ============================================================================
    
    print("\n8. CORRELATION ANALYSIS")
    print("-" * 40)
    
    # Calculate correlation between temperature and humidity
    df_clean = df.dropna(subset=['Temperature', 'Humidity'])
    
    if len(df_clean) > 1:
        correlation = df_clean['Temperature'].corr(df_clean['Humidity'])
        print(f"Temperature-Humidity correlation: {correlation:.3f}")
        
        if abs(correlation) > 0.7:
            relationship = "strong"
        elif abs(correlation) > 0.3:
            relationship = "moderate"
        else:
            relationship = "weak"
            
        direction = "negative" if correlation < 0 else "positive"
        print(f"Interpretation: {relationship} {direction} correlation")
    else:
        print("✗ Insufficient data for correlation analysis")
        correlation = None
    
    # ============================================================================
    # 9. SUMMARY AND INSIGHTS
    # ============================================================================
    
    print("\n9. SUMMARY AND INSIGHTS")
    print("=" * 40)
    
    insights = []
    
    # Data quality insights
    missing_percentage = (df.isnull().sum().sum() / (len(df) * len(df.columns))) * 100
    if missing_percentage < 5:
        insights.append("✓ Excellent data quality with minimal missing values")
    elif missing_percentage < 15:
        insights.append("⚠ Good data quality with some missing values")
    else:
        insights.append("⚠ Data quality issues - significant missing values")
    
    # Temperature insights
    if temp_stats:
        if temp_stats['min'] < 0 and temp_stats['max'] > 20:
            insights.append("✓ Wide temperature range suggests seasonal monitoring")
        if temp_stats['mean'] < 10:
            insights.append("• Average temperature indicates cold climate or winter period")
        elif temp_stats['mean'] > 25:
            insights.append("• Average temperature indicates warm climate or summer period")
    
    # Humidity insights
    if humidity_stats:
        if humidity_stats['mean'] > 70:
            insights.append("• High average humidity suggests indoor or humid environment")
        elif humidity_stats['mean'] < 40:
            insights.append("• Low average humidity suggests dry environment")
    
    # Device insights
    if len(device_counts) > 10:
        insights.append("✓ Large-scale deployment with multiple IoT devices")
    
    # Time series insights
    if time_stats and time_stats['duration_days'] > 30:
        insights.append("✓ Long-term monitoring suitable for trend analysis")
    
    print("Key Insights:")
    for insight in insights:
        print(f"  {insight}")
    
    # ============================================================================
    # 10. RECOMMENDATIONS
    # ============================================================================
    
    print("\n10. RECOMMENDATIONS FOR FURTHER ANALYSIS")
    print("=" * 50)
    
    recommendations = [
        "1. Time Series Visualization: Plot temperature and humidity over time",
        "2. Seasonal Analysis: Analyze patterns by month/season",
        "3. Anomaly Detection: Identify unusual readings or sensor malfunctions",
        "4. Device Comparison: Compare performance across different device models",
        "5. Predictive Modeling: Use historical data for environmental forecasting",
        "6. Clustering Analysis: Group similar environmental conditions",
        "7. Statistical Tests: Perform significance tests between device types"
    ]
    
    for recommendation in recommendations:
        print(f"  {recommendation}")
    
    # ============================================================================
    # 11. RETURN RESULTS
    # ============================================================================
    
    results = {
        'dataset_info': {
            'total_records': total_records,
            'unique_devices': unique_devices,
            'columns': list(df.columns)
        },
        'data_quality': {
            'missing_data': missing_data.to_dict(),
            'missing_percentage': missing_percentage
        },
        'temperature_stats': temp_stats,
        'humidity_stats': humidity_stats,
        'time_stats': time_stats,
        'device_counts': device_counts.to_dict(),
        'device_type_counts': device_type_counts.to_dict(),
        'correlation': correlation,
        'insights': insights,
        'dataframe': df  # Include the processed dataframe
    }
    
    print(f"\n{'='*60}")
    print("ANALYSIS COMPLETE")
    print(f"{'='*60}")
    
    return results

# ============================================================================
# USAGE EXAMPLE
# ============================================================================

if __name__ == "__main__":
    # Replace with your CSV file path
    csv_file_path = "Data/filtered_devices_iot_sensor_data_2025-06-06_corrected_time.csv"
    
    # Run the comprehensive analysis
    analysis_results = comprehensive_iot_analysis(csv_file_path)
    
    # Access specific results if needed
    if analysis_results:
        print(f"\nQuick Access Examples:")
        print(f"Total records: {analysis_results['dataset_info']['total_records']:,}")
        if analysis_results['temperature_stats']:
            print(f"Average temperature: {analysis_results['temperature_stats']['mean']:.1f}°C")
        if analysis_results['humidity_stats']:
            print(f"Average humidity: {analysis_results['humidity_stats']['mean']:.1f}%")

# ============================================================================
# OPTIONAL: VISUALIZATION FUNCTIONS
# ============================================================================

def create_visualizations(analysis_results, save_plots=False):
    """
    Create visualizations based on analysis results.
    
    Parameters:
    analysis_results (dict): Results from comprehensive_iot_analysis function
    save_plots (bool): Whether to save plots to files
    """
    
    df = analysis_results['dataframe']
    
    # Set up the plotting style
    plt.style.use('default')
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('IoT Sensor Data Analysis Dashboard', fontsize=16, fontweight='bold')
    
    # 1. Temperature Distribution
    df['Temperature'].dropna().hist(bins=50, ax=axes[0,0], alpha=0.7, color='blue')
    axes[0,0].set_title('Temperature Distribution')
    axes[0,0].set_xlabel('Temperature (°C)')
    axes[0,0].set_ylabel('Frequency')
    
    # 2. Humidity Distribution
    df['Humidity'].dropna().hist(bins=50, ax=axes[0,1], alpha=0.7, color='green')
    axes[0,1].set_title('Humidity Distribution')
    axes[0,1].set_xlabel('Humidity (%)')
    axes[0,1].set_ylabel('Frequency')
    
    # 3. Device Records Count
    device_counts = analysis_results['device_counts']
    top_devices = dict(list(device_counts.items())[:10])
    
    axes[1,0].bar(range(len(top_devices)), list(top_devices.values()))
    axes[1,0].set_title('Top 10 Devices by Record Count')
    axes[1,0].set_xlabel('Device')
    axes[1,0].set_ylabel('Record Count')
    axes[1,0].set_xticks(range(len(top_devices)))
    axes[1,0].set_xticklabels(list(top_devices.keys()), rotation=45, ha='right')
    
    # 4. Temperature vs Humidity Scatter Plot
    df_clean = df.dropna(subset=['Temperature', 'Humidity'])
    if len(df_clean) > 0:
        sample_size = min(5000, len(df_clean))  # Sample for performance
        df_sample = df_clean.sample(n=sample_size)
        
        axes[1,1].scatter(df_sample['Temperature'], df_sample['Humidity'], 
                         alpha=0.5, s=1, color='red')
        axes[1,1].set_title('Temperature vs Humidity')
        axes[1,1].set_xlabel('Temperature (°C)')
        axes[1,1].set_ylabel('Humidity (%)')
    
    plt.tight_layout()
    
    if save_plots:
        plt.savefig('iot_analysis_dashboard.png', dpi=300, bbox_inches='tight')
        print("✓ Dashboard saved as 'iot_analysis_dashboard.png'")
    
    plt.show()

# Example usage for visualizations:
create_visualizations(analysis_results, save_plots=True)