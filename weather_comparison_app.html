
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laval University IoT vs Public Weather Data</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .data-card {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
        }
        .public-card {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }
        .metric {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .label {
            font-size: 1.2em;
            opacity: 0.9;
        }
        .info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        .coordinates {
            color: #666;
            font-style: italic;
        }
        #weatherData {
            margin-top: 20px;
            padding: 20px;
            background: #e8f5e8;
            border-radius: 10px;
        }
        button {
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Weather Data Comparison</h1>
        <h2>Laval University Campus IoT Network vs Public Weather Stations</h2>
        
        <div class="comparison-grid">
            <div class="data-card">
                <div class="metric">-0.8°C</div>
                <div class="label">IoT Network Average Temperature</div>
                <div style="margin-top: 15px;">
                    <div style="font-size: 1.5em;">69.2%</div>
                    <div style="font-size: 0.9em;">Average Humidity</div>
                </div>
            </div>
            
            <div class="data-card public-card">
                <div class="metric" id="publicTemp">--°C</div>
                <div class="label">Public Weather Station</div>
                <div style="margin-top: 15px;">
                    <div style="font-size: 1.5em;" id="publicHum">--%</div>
                    <div style="font-size: 0.9em;">Humidity</div>
                </div>
            </div>
        </div>
        
        <div class="info">
            <h3>Study Location</h3>
            <p class="coordinates">Latitude: 46.782835771899464, Longitude: -71.27038736335324</p>
            <p>Laval University Campus, Quebec City, Canada</p>
            
            <button onclick="fetchWeatherData()">Fetch Current Weather Data</button>
            <button onclick="compareData()">Compare with IoT Data</button>
            
            <div id="weatherData"></div>
            <div id="comparison"></div>
        </div>
    </div>

    <script>
        async function fetchWeatherData() {
            const lat = 46.782835771899464;
            const lon = -71.27038736335324;
            
            // Using OpenWeatherMap API (requires API key)
            // For demonstration, we'll show mock data
            const mockWeatherData = {
                temperature: -5.2,
                humidity: 78,
                description: "Overcast clouds",
                station: "Quebec City Airport"
            };
            
            document.getElementById('publicTemp').textContent = mockWeatherData.temperature + '°C';
            document.getElementById('publicHum').textContent = mockWeatherData.humidity + '%';
            
            document.getElementById('weatherData').innerHTML = `
                <h4>Latest Public Weather Data</h4>
                <p><strong>Station:</strong> ${mockWeatherData.station}</p>
                <p><strong>Conditions:</strong> ${mockWeatherData.description}</p>
                <p><strong>Temperature:</strong> ${mockWeatherData.temperature}°C</p>
                <p><strong>Humidity:</strong> ${mockWeatherData.humidity}%</p>
                <p><em>Note: This is mock data. For real implementation, integrate with OpenWeatherMap API.</em></p>
            `;
        }
        
        function compareData() {
            const iotTemp = -0.8;
            const iotHum = 69.2;
            const publicTemp = -5.2; // Mock data
            const publicHum = 78; // Mock data
            
            const tempDiff = iotTemp - publicTemp;
            const humDiff = iotHum - publicHum;
            
            document.getElementById('comparison').innerHTML = `
                <h4>Comparison Analysis</h4>
                <p><strong>Temperature Difference:</strong> ${tempDiff > 0 ? '+' : ''}${tempDiff.toFixed(1)}°C</p>
                <p><strong>Humidity Difference:</strong> ${humDiff > 0 ? '+' : ''}${humDiff.toFixed(1)}%</p>
                <p><strong>Analysis:</strong> ${Math.abs(tempDiff) < 2 ? 'Good agreement' : 'Significant difference'} in temperature measurements.</p>
                <p><em>Differences may be due to microclimate effects, sensor placement, or measurement timing.</em></p>
            `;
        }
    </script>
</body>
</html>
        