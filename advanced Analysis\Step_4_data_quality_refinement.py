import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def detect_problematic_periods_and_devices(csv_file_path):
    """
    Advanced data quality analysis to detect problematic periods and devices
    based on visual inspection findings.
    
    Parameters:
    csv_file_path (str): Path to the spatial analysis CSV file
    
    Returns:
    dict: Detailed analysis of data quality issues
    """
    
    print("="*80)
    print("ADVANCED DATA QUALITY REFINEMENT - VISUAL VALIDATION BASED")
    print("="*80)
    
    # Load data
    df = pd.read_csv(csv_file_path)
    time_col = 'time_converted' if 'time_converted' in df.columns else 'cleaned_time'
    df[time_col] = pd.to_datetime(df[time_col])
    df['Temperature'] = pd.to_numeric(df['Temperature'], errors='coerce')
    df['Humidity'] = pd.to_numeric(df['Humidity'], errors='coerce')
    df = df.sort_values(time_col)
    
    print(f"Analyzing {len(df):,} records from {df[time_col].min().date()} to {df[time_col].max().date()}")
    
    # ============================================================================
    # 1. DETECT LINEAR SLOPE PERIODS (STUCK SENSORS)
    # ============================================================================
    
    print("\n1. DETECTING STUCK/FROZEN SENSORS...")
    
    def detect_linear_periods(device_data, time_col, value_col, min_duration_hours=3):
        """Detect periods where sensor values change linearly (indicating stuck sensor)"""
        
        if len(device_data) < 10:
            return []
        
        # Sort by time
        device_data = device_data.sort_values(time_col)
        
        # Calculate rate of change
        device_data['time_diff'] = device_data[time_col].diff().dt.total_seconds() / 3600  # hours
        device_data['value_diff'] = device_data[value_col].diff()
        device_data['rate_of_change'] = device_data['value_diff'] / device_data['time_diff']
        
        # Find periods with constant rate of change (linear slopes)
        linear_periods = []
        current_period = None
        
        for i in range(1, len(device_data) - 1):
            if pd.notna(device_data.iloc[i]['rate_of_change']):
                current_rate = device_data.iloc[i]['rate_of_change']
                
                # Check if rate is suspiciously constant
                if abs(current_rate) < 0.01:  # Very small change (stuck sensor)
                    if current_period is None:
                        current_period = {
                            'start_time': device_data.iloc[i][time_col],
                            'start_idx': i,
                            'rate': current_rate
                        }
                    elif abs(current_rate - current_period['rate']) < 0.005:
                        # Continue current period
                        continue
                    else:
                        # End current period
                        if current_period:
                            duration = (device_data.iloc[i-1][time_col] - current_period['start_time']).total_seconds() / 3600
                            if duration >= min_duration_hours:
                                linear_periods.append({
                                    'start_time': current_period['start_time'],
                                    'end_time': device_data.iloc[i-1][time_col],
                                    'duration_hours': duration,
                                    'rate': current_period['rate'],
                                    'type': 'stuck_sensor' if abs(current_rate) < 0.001 else 'linear_drift'
                                })
                        current_period = None
                else:
                    # End current period if exists
                    if current_period:
                        duration = (device_data.iloc[i-1][time_col] - current_period['start_time']).total_seconds() / 3600
                        if duration >= min_duration_hours:
                            linear_periods.append({
                                'start_time': current_period['start_time'],
                                'end_time': device_data.iloc[i-1][time_col],
                                'duration_hours': duration,
                                'rate': current_period['rate'],
                                'type': 'stuck_sensor' if abs(current_period['rate']) < 0.001 else 'linear_drift'
                            })
                        current_period = None
        
        return linear_periods
    
    problematic_devices = {}
    
    for device in sorted(df['device_name'].unique()):
        device_data = df[df['device_name'] == device].dropna(subset=['Temperature', 'Humidity'])
        
        if len(device_data) < 10:
            continue
            
        # Check temperature linearity
        temp_issues = detect_linear_periods(device_data, time_col, 'Temperature')
        hum_issues = detect_linear_periods(device_data, time_col, 'Humidity')
        
        if temp_issues or hum_issues:
            problematic_devices[device] = {
                'temperature_issues': temp_issues,
                'humidity_issues': hum_issues
            }
    
    print(f"Found {len(problematic_devices)} devices with stuck/linear sensor periods:")
    for device, issues in problematic_devices.items():
        temp_count = len(issues['temperature_issues'])
        hum_count = len(issues['humidity_issues'])
        if temp_count > 0 or hum_count > 0:
            print(f"  {device}: {temp_count} temp issues, {hum_count} humidity issues")
    
    # ============================================================================
    # 2. DETECT DATA GAP PERIODS
    # ============================================================================
    
    print("\n2. DETECTING DATA GAPS AND CONNECTIVITY ISSUES...")
    
    # Create daily record counts
    df['date'] = df[time_col].dt.date
    daily_device_counts = df.groupby(['date', 'device_name']).size().unstack(fill_value=0)
    
    # Expected records per day (assuming ~20 minute intervals = ~72 records/day)
    expected_daily_records = 60  # Minimum acceptable
    
    # Find problematic days and devices
    gap_analysis = {}
    
    for device in daily_device_counts.columns:
        device_series = daily_device_counts[device]
        
        # Find days with very few records
        low_data_days = device_series[device_series < expected_daily_records].index.tolist()
        zero_data_days = device_series[device_series == 0].index.tolist()
        
        if low_data_days or zero_data_days:
            gap_analysis[device] = {
                'low_data_days': low_data_days,
                'zero_data_days': zero_data_days,
                'total_problematic_days': len(set(low_data_days + zero_data_days)),
                'avg_daily_records': device_series.mean(),
                'data_reliability': (len(device_series) - len(set(low_data_days + zero_data_days))) / len(device_series) * 100
            }
    
    print(f"Data gap analysis for {len(gap_analysis)} devices:")
    for device, analysis in sorted(gap_analysis.items(), key=lambda x: x[1]['data_reliability']):
        print(f"  {device}: {analysis['total_problematic_days']} problematic days, "
              f"{analysis['data_reliability']:.1f}% reliable, "
              f"avg {analysis['avg_daily_records']:.1f} records/day")
    
    # ============================================================================
    # 3. IDENTIFY CLEAN PERIODS FOR SPATIAL ANALYSIS
    # ============================================================================
    
    print("\n3. IDENTIFYING TRULY CLEAN PERIODS...")
    
    # Find periods where most devices have good data
    daily_stats = pd.DataFrame(index=daily_device_counts.index)
    daily_stats['devices_with_good_data'] = (daily_device_counts >= expected_daily_records).sum(axis=1)
    daily_stats['total_devices'] = len(daily_device_counts.columns)
    daily_stats['good_coverage_percentage'] = (daily_stats['devices_with_good_data'] / daily_stats['total_devices']) * 100
    
    # Find continuous periods with high good coverage
    def find_clean_continuous_periods(daily_stats, min_good_coverage=80, min_days=7):
        periods = []
        current_period = None
        
        for date, row in daily_stats.iterrows():
            if row['good_coverage_percentage'] >= min_good_coverage:
                if current_period is None:
                    current_period = {'start_date': date, 'days': [], 'coverages': []}
                current_period['days'].append(date)
                current_period['coverages'].append(row['good_coverage_percentage'])
            else:
                if current_period and len(current_period['days']) >= min_days:
                    current_period['end_date'] = current_period['days'][-1]
                    current_period['length_days'] = len(current_period['days'])
                    current_period['avg_coverage'] = np.mean(current_period['coverages'])
                    current_period['min_coverage'] = np.min(current_period['coverages'])
                    periods.append(current_period)
                current_period = None
        
        # Handle final period
        if current_period and len(current_period['days']) >= min_days:
            current_period['end_date'] = current_period['days'][-1]
            current_period['length_days'] = len(current_period['days'])
            current_period['avg_coverage'] = np.mean(current_period['coverages'])
            current_period['min_coverage'] = np.min(current_period['coverages'])
            periods.append(current_period)
        
        return periods
    
    clean_periods = find_clean_continuous_periods(daily_stats, min_good_coverage=85, min_days=7)
    
    print(f"Found {len(clean_periods)} truly clean periods:")
    for i, period in enumerate(clean_periods, 1):
        print(f"  Period {i}: {period['start_date']} to {period['end_date']}")
        print(f"    Length: {period['length_days']} days")
        print(f"    Coverage: {period['avg_coverage']:.1f}% avg, {period['min_coverage']:.1f}% min")
    
    # ============================================================================
    # 4. RECOMMEND DEVICES FOR SPATIAL ANALYSIS
    # ============================================================================
    
    print("\n4. REFINED DEVICE RECOMMENDATIONS...")
    
    # Calculate refined device quality scores
    device_quality_refined = {}
    
    for device in sorted(df['device_name'].unique()):
        # Basic stats
        device_data = df[df['device_name'] == device]
        total_records = len(device_data)
        temp_completeness = len(device_data.dropna(subset=['Temperature'])) / total_records * 100
        hum_completeness = len(device_data.dropna(subset=['Humidity'])) / total_records * 100
        
        # Penalty for problematic periods
        linearity_penalty = 0
        if device in problematic_devices:
            temp_issues = len(problematic_devices[device]['temperature_issues'])
            hum_issues = len(problematic_devices[device]['humidity_issues'])
            linearity_penalty = (temp_issues + hum_issues) * 5  # 5% penalty per issue
        
        # Penalty for data gaps
        gap_penalty = 0
        if device in gap_analysis:
            gap_penalty = (100 - gap_analysis[device]['data_reliability'])
        
        # Calculate refined quality score
        base_quality = (temp_completeness + hum_completeness) / 2
        refined_quality = max(0, base_quality - linearity_penalty - gap_penalty)
        
        device_quality_refined[device] = {
            'base_quality': base_quality,
            'linearity_penalty': linearity_penalty,
            'gap_penalty': gap_penalty,
            'refined_quality': refined_quality,
            'total_records': total_records,
            'temp_completeness': temp_completeness,
            'hum_completeness': hum_completeness
        }
    
    # Sort devices by refined quality
    sorted_devices = sorted(device_quality_refined.items(), key=lambda x: x[1]['refined_quality'], reverse=True)
    
    print("REFINED DEVICE RANKING (after visual validation penalties):")
    excellent_refined = []
    good_refined = []
    
    for i, (device, stats) in enumerate(sorted_devices, 1):
        status = "🟢 EXCELLENT" if stats['refined_quality'] >= 90 else \
                "🟡 GOOD" if stats['refined_quality'] >= 80 else \
                "🔴 PROBLEMATIC"
        
        print(f"{i:2d}. {device:12s} - Quality: {stats['refined_quality']:5.1f}% {status}")
        print(f"    Base: {stats['base_quality']:5.1f}%, Linearity penalty: -{stats['linearity_penalty']:4.1f}%, Gap penalty: -{stats['gap_penalty']:4.1f}%")
        
        if stats['refined_quality'] >= 90:
            excellent_refined.append(device)
        elif stats['refined_quality'] >= 80:
            good_refined.append(device)
    
    # ============================================================================
    # 5. FINAL RECOMMENDATIONS
    # ============================================================================
    
    print("\n5. FINAL REFINED RECOMMENDATIONS FOR SPATIAL ANALYSIS")
    print("="*60)
    
    if clean_periods and excellent_refined:
        best_clean_period = max(clean_periods, key=lambda x: x['length_days'])
        
        print(f"\n🎯 REFINED RECOMMENDATION:")
        print(f"   Period: {best_clean_period['start_date']} to {best_clean_period['end_date']}")
        print(f"   Duration: {best_clean_period['length_days']} days")
        print(f"   Coverage: {best_clean_period['avg_coverage']:.1f}% average")
        print(f"   Devices: {len(excellent_refined)} excellent quality devices")
        print(f"   Device list: {', '.join(excellent_refined)}")
        
        # Filter data for final recommendation
        period_data = df[
            (df['date'] >= best_clean_period['start_date']) & 
            (df['date'] <= best_clean_period['end_date']) &
            (df['device_name'].isin(excellent_refined))
        ]
        
        print(f"\n📊 REFINED DATASET SUMMARY:")
        print(f"   Total records: {len(period_data):,}")
        print(f"   Devices: {period_data['device_name'].nunique()}")
        print(f"   Date range: {period_data[time_col].min().date()} to {period_data[time_col].max().date()}")
        print(f"   Average records per device per day: {len(period_data) / (best_clean_period['length_days'] * len(excellent_refined)):.1f}")
        
        # Save refined dataset
        output_filename = f"refined_spatial_data_{best_clean_period['start_date']}_{best_clean_period['end_date']}.csv"
        period_data.to_csv(output_filename, index=False)
        print(f"   ✅ Refined dataset saved as: {output_filename}")
        
    else:
        print("❌ No suitable clean periods or devices found with refined criteria")
    
    return {
        'problematic_devices': problematic_devices,
        'gap_analysis': gap_analysis,
        'clean_periods': clean_periods,
        'device_quality_refined': device_quality_refined,
        'excellent_devices': excellent_refined,
        'good_devices': good_refined,
        'daily_stats': daily_stats
    }

def create_refined_visualization(analysis_results, csv_file_path):
    """
    Create visualizations showing the refined analysis results.
    """
    
    print("\nCreating refined data quality visualizations...")
    
    # Load original data
    df = pd.read_csv(csv_file_path)
    time_col = 'time_converted' if 'time_converted' in df.columns else 'cleaned_time'
    df[time_col] = pd.to_datetime(df[time_col])
    df['date'] = df[time_col].dt.date
    
    fig, axes = plt.subplots(2, 2, figsize=(20, 12))
    fig.suptitle('Refined Data Quality Analysis - Post Visual Validation', fontsize=16, fontweight='bold')
    
    # 1. Refined device quality scores
    ax1 = axes[0, 0]
    
    device_quality = analysis_results['device_quality_refined']
    devices = list(device_quality.keys())
    base_scores = [device_quality[d]['base_quality'] for d in devices]
    refined_scores = [device_quality[d]['refined_quality'] for d in devices]
    
    x = np.arange(len(devices))
    width = 0.35
    
    ax1.bar(x - width/2, base_scores, width, label='Original Quality', alpha=0.7, color='skyblue')
    ax1.bar(x + width/2, refined_scores, width, label='Refined Quality', alpha=0.7, color='orange')
    
    ax1.set_title('Device Quality: Original vs Refined Scores')
    ax1.set_ylabel('Quality Score (%)')
    ax1.set_xlabel('Device')
    ax1.set_xticks(x)
    ax1.set_xticklabels(devices, rotation=45, ha='right')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.axhline(y=90, color='green', linestyle='--', alpha=0.7, label='Excellent threshold')
    ax1.axhline(y=80, color='orange', linestyle='--', alpha=0.7, label='Good threshold')
    
    # 2. Clean periods timeline
    ax2 = axes[0, 1]
    
    daily_stats = analysis_results['daily_stats']
    ax2.plot(daily_stats.index, daily_stats['good_coverage_percentage'], 
             linewidth=2, color='green', label='Good Coverage %')
    
    # Highlight clean periods
    clean_periods = analysis_results['clean_periods']
    for period in clean_periods:
        ax2.axvspan(period['start_date'], period['end_date'], 
                   alpha=0.3, color='lightgreen', label='Clean Period' if period == clean_periods[0] else "")
    
    ax2.set_title('Clean Periods Identification')
    ax2.set_ylabel('Good Coverage Percentage')
    ax2.set_xlabel('Date')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    ax2.axhline(y=85, color='red', linestyle='--', alpha=0.7, label='Clean threshold')
    
    # 3. Problematic device issues
    ax3 = axes[1, 0]
    
    problematic_devices = analysis_results['problematic_devices']
    gap_analysis = analysis_results['gap_analysis']
    
    devices_with_issues = set(list(problematic_devices.keys()) + list(gap_analysis.keys()))
    issue_types = ['Linear Issues', 'Gap Issues', 'Both Issues']
    issue_counts = [0, 0, 0]
    
    for device in devices_with_issues:
        has_linear = device in problematic_devices
        has_gaps = device in gap_analysis
        
        if has_linear and has_gaps:
            issue_counts[2] += 1
        elif has_linear:
            issue_counts[0] += 1
        elif has_gaps:
            issue_counts[1] += 1
    
    ax3.bar(issue_types, issue_counts, color=['red', 'orange', 'darkred'], alpha=0.7)
    ax3.set_title('Types of Data Quality Issues Found')
    ax3.set_ylabel('Number of Devices')
    ax3.grid(True, alpha=0.3)
    
    # Add text annotations
    for i, count in enumerate(issue_counts):
        ax3.text(i, count + 0.1, str(count), ha='center', fontweight='bold')
    
    # 4. Final recommendation summary
    ax4 = axes[1, 1]
    
    excellent_devices = analysis_results['excellent_devices']
    good_devices = analysis_results['good_devices']
    total_devices = len(device_quality)
    problematic_count = total_devices - len(excellent_devices) - len(good_devices)
    
    categories = ['Excellent\n(≥90%)', 'Good\n(80-90%)', 'Problematic\n(<80%)']
    counts = [len(excellent_devices), len(good_devices), problematic_count]
    colors = ['green', 'orange', 'red']
    
    wedges, texts, autotexts = ax4.pie(counts, labels=categories, colors=colors, 
                                      autopct='%1.1f%%', startangle=90)
    ax4.set_title('Final Device Classification\n(After Visual Validation)')
    
    plt.tight_layout()
    plt.savefig('refined_data_quality_analysis.png', dpi=300, bbox_inches='tight')
    print("✅ Refined analysis saved as 'refined_data_quality_analysis.png'")
    plt.show()

# ============================================================================
# USAGE
# ============================================================================

if __name__ == "__main__":
    csv_file_path = "spatial_analysis_data_20250212_20250514.csv"
    
    # Run refined analysis
    results = detect_problematic_periods_and_devices(csv_file_path)
    
    # Create refined visualizations
    create_refined_visualization(results, csv_file_path)
