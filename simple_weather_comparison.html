<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IoT vs Weather Station Comparison - Laval University</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 30px;
        }
        
        .input-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .input-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #34495e;
        }
        
        input[type="file"], input[type="number"], input[type="date"] {
            width: 100%;
            padding: 10px;
            border: 2px solid #bdc3c7;
            border-radius: 5px;
            font-size: 14px;
        }
        
        button {
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        
        button:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .charts-container {
            display: grid;
            grid-template-columns: 1fr;
            gap: 30px;
            margin-top: 30px;
        }
        
        .chart-box {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 20px;
            color: #2c3e50;
        }
        
        canvas {
            max-height: 400px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
        }
        
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        
        .coords {
            text-align: center;
            color: #7f8c8d;
            font-style: italic;
            margin-bottom: 20px;
        }
        
        .simple-inputs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Weather Data Comparison Tool</h1>
        <p class="subtitle">Compare Laval University IoT Sensor Data with Quebec City Weather Station</p>
        <p class="coords">Study Location: 46.782835°N, 71.270387°W</p>
        
        <!-- IoT Data Input Section -->
        <div class="input-section">
            <h3>📊 IoT Sensor Data Input</h3>
            
            <!-- Option 1: Upload CSV File -->
            <div class="input-group">
                <label for="csvFile">Option 1: Upload CSV file from your Python analysis:</label>
                <input type="file" id="csvFile" accept=".csv" onchange="handleCSVUpload(event)">
                <small>Expected format: timestamp, temperature, humidity columns</small>
            </div>
            
            <div style="text-align: center; margin: 20px 0;">
                <strong>OR</strong>
            </div>
            
            <!-- Option 2: Manual Input -->
            <div class="input-group">
                <label>Option 2: Manual Input (From your Python analysis results):</label>
                <div class="simple-inputs">
                    <div>
                        <label for="startDate">Start Date:</label>
                        <input type="date" id="startDate" value="2025-03-10">
                    </div>
                    <div>
                        <label for="endDate">End Date:</label>
                        <input type="date" id="endDate" value="2025-03-20">
                    </div>
                    <div>
                        <label for="avgTemp">Average Temperature (°C):</label>
                        <input type="number" id="avgTemp" step="0.1" placeholder="e.g., -6.5">
                    </div>
                    <div>
                        <label for="avgHumidity">Average Humidity (%):</label>
                        <input type="number" id="avgHumidity" step="0.1" placeholder="e.g., 75.2">
                    </div>
                </div>
            </div>

            <!-- Data Synchronization Options -->
            <div class="input-group">
                <label for="syncMethod">Data Synchronization Method:</label>
                <select id="syncMethod" onchange="updateSyncMethod()" style="width: 100%; padding: 10px; border: 2px solid #bdc3c7; border-radius: 5px;">
                    <option value="hourly_average">Hourly Average (Recommended)</option>
                    <option value="nearest_neighbor">Nearest Neighbor Matching</option>
                    <option value="interpolation">Linear Interpolation</option>
                </select>
                <small>Choose how to align your exact-time IoT data with hourly weather station data</small>
            </div>

            <!-- Timezone Handling -->
            <div class="input-group">
                <label for="iotTimezone">IoT Data Timezone:</label>
                <select id="iotTimezone" style="width: 100%; padding: 10px; border: 2px solid #bdc3c7; border-radius: 5px;">
                    <option value="auto">Auto-detect from data</option>
                    <option value="+04:00">GMT+4 (Your current data)</option>
                    <option value="+00:00">UTC/GMT</option>
                    <option value="-04:00">EDT (Quebec Summer Time)</option>
                    <option value="-05:00">EST (Quebec Winter Time)</option>
                </select>
                <small>⚠️ Critical: Your IoT data (+04:00) is 8 hours ahead of Quebec time (-04:00)</small>
            </div>
            
            <div style="text-align: center;">
                <button onclick="fetchWeatherData()">🌦️ Fetch Quebec City Weather & Compare</button>
                <button onclick="resetData()">🔄 Reset All Data</button>
            </div>
        </div>
        
        <!-- Status Messages -->
        <div id="statusMessages"></div>
        
        <!-- Statistics Display -->
        <div id="statisticsSection" style="display: none;">
            <h3>📈 Comparison Statistics</h3>
            <div class="stats-grid" id="statsGrid"></div>
        </div>

        <!-- Synchronization Quality Display -->
        <div id="syncQualitySection" style="display: none;">
            <h3>🔄 Data Synchronization Quality</h3>
            <div class="stats-grid" id="syncQualityGrid"></div>
        </div>
        
        <!-- Charts -->
        <div class="charts-container">
            <div class="chart-box">
                <div class="chart-title">Temperature Comparison (°C)</div>
                <canvas id="temperatureChart"></canvas>
            </div>
            
            <div class="chart-box">
                <div class="chart-title">Humidity Comparison (%)</div>
                <canvas id="humidityChart"></canvas>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let iotData = [];
        let weatherData = [];
        let synchronizedData = [];
        let tempChart = null;
        let humidityChart = null;
        let syncMethod = 'hourly_average'; // 'hourly_average', 'nearest_neighbor', 'interpolation'
        
        // Laval University coordinates
        const LAVAL_LAT = 46.782835771899464;
        const LAVAL_LON = -71.27038736335324;
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('statusMessages');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
            setTimeout(() => {
                statusDiv.innerHTML = '';
            }, 5000);
        }

        function updateSyncMethod() {
            syncMethod = document.getElementById('syncMethod').value;
            if (iotData.length > 0 && weatherData.length > 0) {
                synchronizeData();
                updateStatistics();
                updateCharts();
            }
        }

        function convertToQuebecTime(timestampStr) {
            // Convert timezone-aware timestamp to Quebec time (America/New_York = GMT-4)
            try {
                // Extract timezone from the string
                let timezoneMatch = timestampStr.match(/([+-]\d{2}):?(\d{2})$/);
                if (!timezoneMatch) {
                    // Try to get from dropdown
                    const selectedTz = document.getElementById('iotTimezone').value;
                    if (selectedTz !== 'auto') {
                        timezoneMatch = selectedTz.match(/([+-]\d{2}):?(\d{2})/);
                    }
                }

                if (timezoneMatch) {
                    const sign = timezoneMatch[1].charAt(0) === '+' ? 1 : -1;
                    const hours = parseInt(timezoneMatch[1].substring(1));
                    const minutes = parseInt(timezoneMatch[2] || '0');
                    const iotOffsetMinutes = sign * (hours * 60 + minutes);

                    // Quebec is GMT-4 (EDT) in March
                    const quebecOffsetMinutes = -4 * 60;

                    // Parse timestamp without timezone
                    const cleanTimestamp = timestampStr.replace(/[+-]\d{2}:?\d{2}$/, '').trim();
                    const baseDate = new Date(cleanTimestamp);

                    // Convert from IoT timezone to Quebec timezone
                    const offsetDiffMinutes = quebecOffsetMinutes - iotOffsetMinutes;
                    const quebecTime = new Date(baseDate.getTime() + offsetDiffMinutes * 60 * 1000);

                    return quebecTime;
                } else {
                    // No timezone info, assume it's already in Quebec time
                    return new Date(timestampStr);
                }
            } catch (error) {
                console.warn('Timezone conversion failed, using original timestamp:', error);
                return new Date(timestampStr);
            }
        }
        
        function handleCSVUpload(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const csv = e.target.result;
                    const lines = csv.split('\n').filter(line => line.trim().length > 0);

                    // Check if first line looks like headers or data
                    const firstLine = lines[0].split(',');
                    let hasHeaders = false;
                    let timeIndex = -1, tempIndex = -1, humIndex = -1;

                    // Try to detect headers
                    const headers = firstLine.map(h => h.trim().toLowerCase());
                    timeIndex = headers.findIndex(h => h.includes('time') || h.includes('date') || h.includes('timestamp'));
                    tempIndex = headers.findIndex(h => h.includes('temp'));
                    humIndex = headers.findIndex(h => h.includes('hum'));

                    if (timeIndex !== -1 && tempIndex !== -1 && humIndex !== -1) {
                        hasHeaders = true;
                    } else {
                        // No headers detected, assume format based on your example:
                        // 87118,eui-a8404196718607e7,71.3,-8.1,LSN50-3,Mon Mar 10 2025 08:21:34 GMT-0400
                        // Try to auto-detect columns by data patterns
                        showStatus('No headers detected. Attempting to auto-detect column format...', 'info');

                        // Look for timestamp pattern in each column of first few rows
                        for (let col = 0; col < firstLine.length; col++) {
                            const testValue = firstLine[col].trim();
                            // Check if this looks like a timestamp
                            if (testValue.includes('GMT') || testValue.includes(':') ||
                                testValue.match(/\d{4}/) || testValue.includes('Mon|Tue|Wed|Thu|Fri|Sat|Sun')) {
                                timeIndex = col;
                                break;
                            }
                        }

                        // Look for numeric columns that could be temperature/humidity
                        const numericCols = [];
                        for (let col = 0; col < firstLine.length; col++) {
                            const testValue = parseFloat(firstLine[col].trim());
                            if (!isNaN(testValue) && testValue > -50 && testValue < 150) {
                                numericCols.push(col);
                            }
                        }

                        if (numericCols.length >= 2) {
                            tempIndex = numericCols[0]; // First numeric column as temperature
                            humIndex = numericCols[1];  // Second numeric column as humidity
                        }
                    }

                    if (timeIndex === -1 || tempIndex === -1 || humIndex === -1) {
                        showStatus('Could not detect timestamp, temperature, and humidity columns. Please ensure your CSV has the correct format.', 'error');
                        return;
                    }
                    
                    iotData = [];
                    const startRow = hasHeaders ? 1 : 0;

                    for (let i = startRow; i < lines.length; i++) {
                        const values = lines[i].split(',');
                        if (values.length >= Math.max(timeIndex, tempIndex, humIndex) + 1) {
                            let timestampStr = values[timeIndex].trim();

                            // Handle different timestamp formats
                            if (timestampStr.includes('GMT')) {
                                // Format: "Mon Mar 10 2025 08:21:34 GMT-0400"
                                timestampStr = timestampStr.replace(/GMT[+-]\d{4}/, '').trim();
                            }

                            let timestamp = new Date(timestampStr);

                            // Convert to Quebec timezone (America/New_York) if needed
                            // Check if timestamp has timezone info
                            if (timestampStr.includes('+') || timestampStr.includes('-')) {
                                // Parse timezone-aware timestamp and convert to Quebec time
                                timestamp = convertToQuebecTime(timestampStr);
                            }
                            const temperature = parseFloat(values[tempIndex]);
                            const humidity = parseFloat(values[humIndex]);

                            if (!isNaN(timestamp.getTime()) && !isNaN(temperature) && !isNaN(humidity)) {
                                iotData.push({
                                    timestamp: timestamp,
                                    temperature: temperature,
                                    humidity: humidity
                                });
                            }
                        }
                    }
                    
                    if (iotData.length > 0) {
                        // Sort by timestamp
                        iotData.sort((a, b) => a.timestamp - b.timestamp);

                        // Check for timezone conversion
                        const firstTimestamp = iotData[0].timestamp;
                        const lastTimestamp = iotData[iotData.length - 1].timestamp;

                        // Auto-populate date inputs
                        document.getElementById('startDate').value = firstTimestamp.toISOString().split('T')[0];
                        document.getElementById('endDate').value = lastTimestamp.toISOString().split('T')[0];

                        // Calculate averages
                        const avgTemp = iotData.reduce((sum, d) => sum + d.temperature, 0) / iotData.length;
                        const avgHum = iotData.reduce((sum, d) => sum + d.humidity, 0) / iotData.length;

                        document.getElementById('avgTemp').value = avgTemp.toFixed(1);
                        document.getElementById('avgHumidity').value = avgHum.toFixed(1);

                        // Show timezone info
                        const timeRange = `${firstTimestamp.toLocaleString()} to ${lastTimestamp.toLocaleString()}`;
                        showStatus(`Successfully loaded ${iotData.length} IoT sensor readings (${timeRange}) - Timezone converted to Quebec time`, 'success');
                    } else {
                        showStatus('No valid data found in CSV file', 'error');
                    }
                } catch (error) {
                    showStatus('Error parsing CSV file: ' + error.message, 'error');
                }
            };
            reader.readAsText(file);
        }
        
        async function fetchWeatherData() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const avgTemp = parseFloat(document.getElementById('avgTemp').value);
            const avgHum = parseFloat(document.getElementById('avgHumidity').value);
            
            if (!startDate || !endDate) {
                showStatus('Please provide start and end dates', 'error');
                return;
            }
            
            if (isNaN(avgTemp) || isNaN(avgHum)) {
                showStatus('Please provide average temperature and humidity values', 'error');
                return;
            }
            
            showStatus('Fetching weather data...', 'info');
            
            try {
                // Fetch weather data from Open-Meteo (free, no API key needed)
                const start = startDate;
                const end = endDate;
                
                // const weatherUrl = `https://api.open-meteo.com/v1/forecast?latitude=${LAVAL_LAT}&longitude=${LAVAL_LON}&start_date=${start}&end_date=${end}&hourly=temperature_2m,relative_humidity_2m&timezone=America/New_York`;
                const weatherUrl = `https://archive-api.open-meteo.com/v1/archive?latitude=46.782835771899464&longitude=-71.27038736335324&start_date=2025-03-10&end_date=2025-03-20&hourly=temperature_2m,relative_humidity_2m&timezone=America/New_York`;
                
                const response = await fetch(weatherUrl);
                if (!response.ok) {
                    throw new Error('Failed to fetch weather data');
                }
                
                const data = await response.json();


                // Drop first 7 elements (00:00 to 06:00) to start from 07:00                                
                // Process weather data
                weatherData = [];
                const times = data.hourly.time.slice(8);
                const temps = data.hourly.temperature_2m.slice(8);
                const humidity = data.hourly.relative_humidity_2m.slice(8);
                
                for (let i = 0; i < times.length; i++) {
                    weatherData.push({
                        timestamp: new Date(times[i]),
                        temperature: temps[i],
                        humidity: humidity[i]
                    });
                }
                
                // If no CSV was uploaded, create simple IoT data based on user input
                if (iotData.length === 0) {
                    createSimpleIoTData(startDate, endDate, avgTemp, avgHum);
                }

                showStatus(`Successfully fetched ${weatherData.length} weather readings`, 'success');

                // Synchronize the datasets
                synchronizeData();

                // Update statistics and charts
                updateStatistics();
                updateCharts();
                
            } catch (error) {
                showStatus('Error fetching weather data: ' + error.message, 'error');
                console.error(error);
            }
        }
        
        function createSimpleIoTData(startDate, endDate, avgTemp, avgHum) {
            // Create realistic IoT sensor data with multiple readings per hour
            const start = new Date(startDate);
            const end = new Date(endDate);
            const totalHours = Math.ceil((end - start) / (1000 * 60 * 60));

            iotData = [];

            // Create multiple readings per hour (2-8 readings per hour, randomly)
            for (let h = 0; h < totalHours; h++) {
                const hourStart = new Date(start.getTime() + h * 60 * 60 * 1000);
                const readingsThisHour = Math.floor(Math.random() * 7) + 2; // 2-8 readings per hour

                for (let r = 0; r < readingsThisHour; r++) {
                    // Random minute within the hour
                    const minutes = Math.floor(Math.random() * 60);
                    const seconds = Math.floor(Math.random() * 60);

                    const timestamp = new Date(hourStart);
                    timestamp.setMinutes(minutes, seconds);

                    // Create realistic temperature variation (daily cycle + noise)
                    const hourOfDay = timestamp.getHours();
                    const dailyCycle = Math.sin((hourOfDay - 6) * Math.PI / 12) * 3; // 6°C daily variation, min at 6am
                    const randomNoise = (Math.random() - 0.5) * 2; // ±1°C random noise
                    const temperature = avgTemp + dailyCycle + randomNoise;

                    // Create realistic humidity variation (inverse relationship with temperature)
                    const humidityBase = avgHum - dailyCycle * 2; // Humidity decreases when temp increases
                    const humidityNoise = (Math.random() - 0.5) * 8; // ±4% random noise
                    const humidity = Math.max(10, Math.min(100, humidityBase + humidityNoise));

                    iotData.push({
                        timestamp: timestamp,
                        temperature: temperature,
                        humidity: humidity
                    });
                }
            }

            // Sort by timestamp
            iotData.sort((a, b) => a.timestamp - b.timestamp);

            showStatus(`Generated ${iotData.length} realistic IoT sensor readings for demonstration`, 'info');
        }

        function synchronizeData() {
            if (iotData.length === 0 || weatherData.length === 0) {
                synchronizedData = [];
                return;
            }

            showStatus(`Synchronizing data using ${syncMethod} method...`, 'info');

            switch (syncMethod) {
                case 'hourly_average':
                    synchronizedData = synchronizeByHourlyAverage();
                    break;
                case 'nearest_neighbor':
                    synchronizedData = synchronizeByNearestNeighbor();
                    break;
                case 'interpolation':
                    synchronizedData = synchronizeByInterpolation();
                    break;
                default:
                    synchronizedData = synchronizeByHourlyAverage();
            }

            // Check for data coverage issues
            if (synchronizedData.length > 0) {
                const iotStart = iotData[0].timestamp;
                const weatherStart = weatherData[0].timestamp;
                const startGapHours = Math.max(0, (iotStart - weatherStart) / (1000 * 60 * 60));

                if (startGapHours > 0.5) {
                    showStatus(`⚠️ Synchronized ${synchronizedData.length} data points. Note: IoT data starts ${startGapHours.toFixed(1)} hours after weather data (missing early hours)`, 'info');
                } else {
                    showStatus(`✓ Synchronized ${synchronizedData.length} data points successfully`, 'success');
                }
            } else {
                showStatus('❌ No data points could be synchronized - check time ranges and timezone settings', 'error');
            }

            updateSyncQuality();
        }

        function synchronizeByHourlyAverage() {
            // Group IoT data by hour and average, then match with weather data
            const hourlyIoT = {};

            // Group IoT data by hour
            iotData.forEach(reading => {
                const hourKey = new Date(reading.timestamp);
                hourKey.setMinutes(0, 0, 0); // Round to hour
                const key = hourKey.getTime();

                if (!hourlyIoT[key]) {
                    hourlyIoT[key] = {
                        timestamp: hourKey,
                        temperatures: [],
                        humidities: [],
                        count: 0
                    };
                }

                hourlyIoT[key].temperatures.push(reading.temperature);
                hourlyIoT[key].humidities.push(reading.humidity);
                hourlyIoT[key].count++;
            });

            // Calculate averages and match with weather data
            const synchronized = [];
            Object.values(hourlyIoT).forEach(hourlyData => {
                const avgTemp = hourlyData.temperatures.reduce((a, b) => a + b, 0) / hourlyData.temperatures.length;
                const avgHum = hourlyData.humidities.reduce((a, b) => a + b, 0) / hourlyData.humidities.length;

                // Find matching weather data (exact hour match)
                const weatherMatch = weatherData.find(w => {
                    const timeDiff = Math.abs(w.timestamp.getTime() - hourlyData.timestamp.getTime());
                    return timeDiff < 30 * 60 * 1000; // Within 30 minutes
                });

                if (weatherMatch) {
                    synchronized.push({
                        timestamp: hourlyData.timestamp,
                        iot_temperature: avgTemp,
                        iot_humidity: avgHum,
                        weather_temperature: weatherMatch.temperature,
                        weather_humidity: weatherMatch.humidity,
                        iot_sample_count: hourlyData.count,
                        sync_method: 'hourly_average'
                    });
                }
            });

            return synchronized.sort((a, b) => a.timestamp - b.timestamp);
        }

        function synchronizeByNearestNeighbor() {
            // For each IoT reading, find the closest weather station reading
            const synchronized = [];
            const maxTimeDiff = 60 * 60 * 1000; // 1 hour in milliseconds

            iotData.forEach(iotReading => {
                let closestWeather = null;
                let minTimeDiff = Infinity;

                weatherData.forEach(weatherReading => {
                    const timeDiff = Math.abs(iotReading.timestamp.getTime() - weatherReading.timestamp.getTime());
                    if (timeDiff < minTimeDiff && timeDiff <= maxTimeDiff) {
                        minTimeDiff = timeDiff;
                        closestWeather = weatherReading;
                    }
                });

                if (closestWeather) {
                    synchronized.push({
                        timestamp: iotReading.timestamp,
                        iot_temperature: iotReading.temperature,
                        iot_humidity: iotReading.humidity,
                        weather_temperature: closestWeather.temperature,
                        weather_humidity: closestWeather.humidity,
                        time_difference_minutes: Math.round(minTimeDiff / (60 * 1000)),
                        sync_method: 'nearest_neighbor'
                    });
                }
            });

            return synchronized.sort((a, b) => a.timestamp - b.timestamp);
        }

        function synchronizeByInterpolation() {
            // Interpolate weather data to match IoT timestamps
            const synchronized = [];

            iotData.forEach(iotReading => {
                const iotTime = iotReading.timestamp.getTime();

                // Find surrounding weather readings
                let beforeWeather = null;
                let afterWeather = null;

                for (let i = 0; i < weatherData.length - 1; i++) {
                    const current = weatherData[i];
                    const next = weatherData[i + 1];

                    if (current.timestamp.getTime() <= iotTime && next.timestamp.getTime() >= iotTime) {
                        beforeWeather = current;
                        afterWeather = next;
                        break;
                    }
                }

                if (beforeWeather && afterWeather) {
                    // Linear interpolation
                    const beforeTime = beforeWeather.timestamp.getTime();
                    const afterTime = afterWeather.timestamp.getTime();
                    const ratio = (iotTime - beforeTime) / (afterTime - beforeTime);

                    const interpolatedTemp = beforeWeather.temperature +
                        (afterWeather.temperature - beforeWeather.temperature) * ratio;
                    const interpolatedHum = beforeWeather.humidity +
                        (afterWeather.humidity - beforeWeather.humidity) * ratio;

                    synchronized.push({
                        timestamp: iotReading.timestamp,
                        iot_temperature: iotReading.temperature,
                        iot_humidity: iotReading.humidity,
                        weather_temperature: interpolatedTemp,
                        weather_humidity: interpolatedHum,
                        interpolation_ratio: ratio,
                        sync_method: 'interpolation'
                    });
                }
            });

            return synchronized.sort((a, b) => a.timestamp - b.timestamp);
        }

        function updateSyncQuality() {
            if (synchronizedData.length === 0) {
                document.getElementById('syncQualitySection').style.display = 'none';
                return;
            }

            const totalIoTReadings = iotData.length;
            const totalWeatherReadings = weatherData.length;
            const synchronizedReadings = synchronizedData.length;

            const syncRate = (synchronizedReadings / totalIoTReadings * 100).toFixed(1);
            const dataLoss = (100 - syncRate).toFixed(1);

            // Calculate time span coverage and gaps
            const iotStart = iotData[0].timestamp;
            const iotEnd = iotData[iotData.length - 1].timestamp;
            const weatherStart = weatherData[0].timestamp;
            const weatherEnd = weatherData[weatherData.length - 1].timestamp;

            const iotTimeSpan = (iotEnd - iotStart) / (1000 * 60 * 60); // hours
            const syncTimeSpan = (synchronizedData[synchronizedData.length - 1].timestamp - synchronizedData[0].timestamp) / (1000 * 60 * 60); // hours
            const coverageRate = (syncTimeSpan / iotTimeSpan * 100).toFixed(1);

            // Check for data gaps
            const startGapHours = Math.max(0, (iotStart - weatherStart) / (1000 * 60 * 60));
            const endGapHours = Math.max(0, (weatherEnd - iotEnd) / (1000 * 60 * 60));
            const hasStartGap = startGapHours > 0.5; // More than 30 minutes gap
            const hasEndGap = endGapHours > 0.5;

            // Method-specific metrics
            let methodSpecificInfo = '';
            if (syncMethod === 'hourly_average') {
                const avgSampleCount = synchronizedData.reduce((sum, d) => sum + (d.iot_sample_count || 1), 0) / synchronizedData.length;
                methodSpecificInfo = `Avg samples/hour: ${avgSampleCount.toFixed(1)}`;
            } else if (syncMethod === 'nearest_neighbor') {
                const avgTimeDiff = synchronizedData.reduce((sum, d) => sum + (d.time_difference_minutes || 0), 0) / synchronizedData.length;
                methodSpecificInfo = `Avg time diff: ${avgTimeDiff.toFixed(1)} min`;
            } else if (syncMethod === 'interpolation') {
                methodSpecificInfo = `Linear interpolation`;
            }

            // Timezone info
            const selectedTz = document.getElementById('iotTimezone').value;
            const timezoneInfo = selectedTz === 'auto' ? 'Auto-detected' : `IoT: ${selectedTz} → QC: GMT-4`;

            // Create gap warning message
            let gapWarning = '';
            if (hasStartGap || hasEndGap) {
                const gaps = [];
                if (hasStartGap) gaps.push(`${startGapHours.toFixed(1)}h at start`);
                if (hasEndGap) gaps.push(`${endGapHours.toFixed(1)}h at end`);
                gapWarning = `⚠️ Data gaps: ${gaps.join(', ')}`;
            }

            const qualityHTML = `
                <div class="stat-card">
                    <div class="stat-number">${syncRate}%</div>
                    <div class="stat-label">Synchronization Rate</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${synchronizedReadings}</div>
                    <div class="stat-label">Synchronized Points</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${dataLoss}%</div>
                    <div class="stat-label">Data Loss</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${coverageRate}%</div>
                    <div class="stat-label">Time Coverage</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${syncMethod.replace('_', ' ')}</div>
                    <div class="stat-label">Sync Method</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${methodSpecificInfo}</div>
                    <div class="stat-label">Method Details</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${timezoneInfo}</div>
                    <div class="stat-label">Timezone Conversion</div>
                </div>
                ${gapWarning ? `<div class="stat-card" style="background: linear-gradient(45deg, #e74c3c, #c0392b);">
                    <div class="stat-number">${gapWarning}</div>
                    <div class="stat-label">Coverage Issues</div>
                </div>` : ''}
            `;

            document.getElementById('syncQualityGrid').innerHTML = qualityHTML;
            document.getElementById('syncQualitySection').style.display = 'block';
        }
        
        function updateStatistics() {
            if (synchronizedData.length === 0) {
                document.getElementById('statisticsSection').style.display = 'none';
                return;
            }

            // Calculate statistics using synchronized data for accurate comparison
            const iotAvgTemp = synchronizedData.reduce((sum, d) => sum + d.iot_temperature, 0) / synchronizedData.length;
            const iotAvgHum = synchronizedData.reduce((sum, d) => sum + d.iot_humidity, 0) / synchronizedData.length;

            const weatherAvgTemp = synchronizedData.reduce((sum, d) => sum + d.weather_temperature, 0) / synchronizedData.length;
            const weatherAvgHum = synchronizedData.reduce((sum, d) => sum + d.weather_humidity, 0) / synchronizedData.length;

            const tempDiff = iotAvgTemp - weatherAvgTemp;
            const humDiff = iotAvgHum - weatherAvgHum;

            // Calculate correlation coefficients
            const tempCorrelation = calculateCorrelation(
                synchronizedData.map(d => d.iot_temperature),
                synchronizedData.map(d => d.weather_temperature)
            );
            const humCorrelation = calculateCorrelation(
                synchronizedData.map(d => d.iot_humidity),
                synchronizedData.map(d => d.weather_humidity)
            );
            
            // Display statistics
            const statsHTML = `
                <div class="stat-card">
                    <div class="stat-number">${iotAvgTemp.toFixed(1)}°C</div>
                    <div class="stat-label">IoT Avg Temperature</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${weatherAvgTemp.toFixed(1)}°C</div>
                    <div class="stat-label">Weather Station Avg</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${tempDiff > 0 ? '+' : ''}${tempDiff.toFixed(1)}°C</div>
                    <div class="stat-label">Temperature Difference</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${iotAvgHum.toFixed(1)}%</div>
                    <div class="stat-label">IoT Avg Humidity</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${weatherAvgHum.toFixed(1)}%</div>
                    <div class="stat-label">Weather Station Humidity</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${humDiff > 0 ? '+' : ''}${humDiff.toFixed(1)}%</div>
                    <div class="stat-label">Humidity Difference</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${tempCorrelation.toFixed(3)}</div>
                    <div class="stat-label">Temperature Correlation</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${humCorrelation.toFixed(3)}</div>
                    <div class="stat-label">Humidity Correlation</div>
                </div>
            `;
            
            document.getElementById('statsGrid').innerHTML = statsHTML;
            document.getElementById('statisticsSection').style.display = 'block';
        }

        function calculateCorrelation(x, y) {
            if (x.length !== y.length || x.length === 0) return 0;

            const n = x.length;
            const sumX = x.reduce((a, b) => a + b, 0);
            const sumY = y.reduce((a, b) => a + b, 0);
            const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
            const sumX2 = x.reduce((sum, xi) => sum + xi * xi, 0);
            const sumY2 = y.reduce((sum, yi) => sum + yi * yi, 0);

            const numerator = n * sumXY - sumX * sumY;
            const denominator = Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));

            return denominator === 0 ? 0 : numerator / denominator;
        }
        
        function updateCharts() {
            // Destroy existing charts
            if (tempChart) tempChart.destroy();
            if (humidityChart) humidityChart.destroy();

            if (weatherData.length === 0) return;

            // Create a complete timeline showing both datasets with gaps
            const allWeatherTimes = weatherData.map(d => d.timestamp.getTime());
            const minTime = Math.min(...allWeatherTimes);
            const maxTime = Math.max(...allWeatherTimes);

            // Create hourly timeline from weather data start to end
            const completeTimeline = [];
            for (let time = minTime; time <= maxTime; time += 60 * 60 * 1000) { // 1 hour steps
                completeTimeline.push(new Date(time));
            }

            // Prepare data arrays with nulls for missing data
            const labels = completeTimeline.map(d =>
                d.toLocaleDateString() + ' ' + d.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})
            );

            const weatherTemps = completeTimeline.map(time => {
                const weatherPoint = weatherData.find(w =>
                    Math.abs(w.timestamp.getTime() - time.getTime()) < 30 * 60 * 1000
                );
                return weatherPoint ? weatherPoint.temperature : null;
            });

            const weatherHums = completeTimeline.map(time => {
                const weatherPoint = weatherData.find(w =>
                    Math.abs(w.timestamp.getTime() - time.getTime()) < 30 * 60 * 1000
                );
                return weatherPoint ? weatherPoint.humidity : null;
            });

            const iotTemps = completeTimeline.map(time => {
                const syncPoint = synchronizedData.find(s =>
                    Math.abs(s.timestamp.getTime() - time.getTime()) < 30 * 60 * 1000
                );
                return syncPoint ? syncPoint.iot_temperature : null;
            });

            const iotHums = completeTimeline.map(time => {
                const syncPoint = synchronizedData.find(s =>
                    Math.abs(s.timestamp.getTime() - time.getTime()) < 30 * 60 * 1000
                );
                return syncPoint ? syncPoint.iot_humidity : null;
            });
            
            // Temperature chart
            const tempCtx = document.getElementById('temperatureChart').getContext('2d');
            tempChart = new Chart(tempCtx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: `IoT Sensors (${syncMethod.replace('_', ' ')})`,
                            data: iotTemps,
                            borderColor: '#e74c3c',
                            backgroundColor: 'rgba(231, 76, 60, 0.1)',
                            tension: 0.1,
                            pointRadius: 3,
                            pointHoverRadius: 5,
                            spanGaps: false // Don't connect across null values - shows gaps
                        },
                        {
                            label: 'Quebec City Weather Station',
                            data: weatherTemps,
                            borderColor: '#3498db',
                            backgroundColor: 'rgba(52, 152, 219, 0.1)',
                            tension: 0.1,
                            pointRadius: 3,
                            pointHoverRadius: 5,
                            spanGaps: false
                        }
                    ]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        }
                    },
                    scales: {
                        y: {
                            title: {
                                display: true,
                                text: 'Temperature (°C)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Date & Time (Complete Timeline - Gaps Show Missing IoT Data)'
                            }
                        }
                    }
                }
            });
            
            // Humidity chart
            const humCtx = document.getElementById('humidityChart').getContext('2d');
            humidityChart = new Chart(humCtx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: `IoT Sensors (${syncMethod.replace('_', ' ')})`,
                            data: iotHums,
                            borderColor: '#27ae60',
                            backgroundColor: 'rgba(39, 174, 96, 0.1)',
                            tension: 0.1,
                            pointRadius: 3,
                            pointHoverRadius: 5,
                            spanGaps: false // Don't connect across null values - shows gaps
                        },
                        {
                            label: 'Quebec City Weather Station',
                            data: weatherHums,
                            borderColor: '#f39c12',
                            backgroundColor: 'rgba(243, 156, 18, 0.1)',
                            tension: 0.1,
                            pointRadius: 3,
                            pointHoverRadius: 5,
                            spanGaps: false
                        }
                    ]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        }
                    },
                    scales: {
                        y: {
                            title: {
                                display: true,
                                text: 'Relative Humidity (%)'
                            },
                            min: 0,
                            max: 100
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Date & Time (Complete Timeline - Gaps Show Missing IoT Data)'
                            }
                        }
                    }
                }
            });
        }
        
        function resetData() {
            iotData = [];
            weatherData = [];
            synchronizedData = [];

            document.getElementById('csvFile').value = '';
            document.getElementById('avgTemp').value = '';
            document.getElementById('avgHumidity').value = '';
            document.getElementById('startDate').value = '2025-03-10';
            document.getElementById('endDate').value = '2025-03-20';
            document.getElementById('syncMethod').value = 'hourly_average';
            syncMethod = 'hourly_average';

            document.getElementById('statisticsSection').style.display = 'none';
            document.getElementById('syncQualitySection').style.display = 'none';

            if (tempChart) tempChart.destroy();
            if (humidityChart) humidityChart.destroy();

            showStatus('Data reset successfully', 'success');
        }
        
        // Initialize with default values
        window.onload = function() {
            showStatus('Ready! Upload your IoT data or enter average values to compare with Quebec City weather station. The app will automatically synchronize your exact-time data with hourly weather readings.', 'info');
        };
    </script>
</body>
</html>