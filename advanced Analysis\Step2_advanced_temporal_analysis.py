import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

def advanced_temporal_analysis_for_spatial(csv_file_path, min_days=7, ideal_days=14):
    """
    Advanced temporal analysis to identify optimal periods for spatial analysis.
    
    Parameters:
    csv_file_path (str): Path to the CSV file
    min_days (int): Minimum acceptable period length in days
    ideal_days (int): Ideal period length in days
    
    Returns:
    dict: Comprehensive analysis results with optimal periods
    """
    
    print("="*80)
    print("ADVANCED TEMPORAL ANALYSIS FOR SPATIAL INVESTIGATION")
    print("="*80)
    
    # Load and prepare data
    print("\n1. LOADING AND PREPARING DATA...")
    df = pd.read_csv(csv_file_path)
    
    # Convert time column
    time_col = 'time_converted' if 'time_converted' in df.columns else 'cleaned_time'
    df[time_col] = pd.to_datetime(df[time_col])
    df['Temperature'] = pd.to_numeric(df['Temperature'], errors='coerce')
    df['Humidity'] = pd.to_numeric(df['Humidity'], errors='coerce')
    
    # Sort by time
    df = df.sort_values(time_col)
    
    print(f"✓ Loaded {len(df):,} records from {df[time_col].min().date()} to {df[time_col].max().date()}")
    print(f"✓ Total devices: {df['device_name'].nunique()}")
    
    # ============================================================================
    # 2. DEVICE ACTIVITY TIMELINE ANALYSIS
    # ============================================================================
    
    print("\n2. DEVICE ACTIVITY TIMELINE ANALYSIS...")
    
    # Create daily device activity matrix
    df['date'] = df[time_col].dt.date
    device_daily_activity = df.groupby(['date', 'device_name']).agg({
        'Temperature': 'count',
        'Humidity': lambda x: x.notna().sum()
    }).unstack(fill_value=0)
    
    # Flatten column names
    temp_activity = device_daily_activity['Temperature']
    humidity_activity = device_daily_activity['Humidity']
    
    # Calculate device availability by day
    dates = pd.date_range(start=df[time_col].min().date(), 
                         end=df[time_col].max().date(), freq='D')
    
    device_availability = pd.DataFrame(index=dates, columns=df['device_name'].unique())
    
    for date in dates:
        date_only = date.date()
        for device in df['device_name'].unique():
            # Check if device has data on this date
            temp_count = temp_activity.loc[date_only, device] if date_only in temp_activity.index else 0
            hum_count = humidity_activity.loc[date_only, device] if date_only in humidity_activity.index else 0
            
            # Consider device active if it has both temp and humidity data
            device_availability.loc[date, device] = 1 if (temp_count > 0 and hum_count > 0) else 0
    
    # Calculate daily statistics
    daily_stats = pd.DataFrame(index=dates)
    daily_stats['active_devices'] = device_availability.sum(axis=1)
    daily_stats['total_devices'] = len(df['device_name'].unique())
    daily_stats['coverage_percentage'] = (daily_stats['active_devices'] / daily_stats['total_devices']) * 100
    
    print(f"✓ Analyzed {len(dates)} days of data")
    print(f"✓ Maximum devices active on any day: {daily_stats['active_devices'].max()}")
    print(f"✓ Average devices active per day: {daily_stats['active_devices'].mean():.1f}")
    
    # ============================================================================
    # 3. IDENTIFY CONTINUOUS PERIODS WITH HIGH COVERAGE
    # ============================================================================
    
    print("\n3. IDENTIFYING OPTIMAL CONTINUOUS PERIODS...")
    
    def find_continuous_periods(daily_stats, min_coverage=70, min_length=min_days):
        """Find continuous periods with minimum coverage percentage."""
        periods = []
        start_date = None
        
        for date, row in daily_stats.iterrows():
            if row['coverage_percentage'] >= min_coverage:
                if start_date is None:
                    start_date = date
            else:
                if start_date is not None:
                    # End of a good period
                    period_length = (date - start_date).days
                    if period_length >= min_length:
                        periods.append({
                            'start_date': start_date,
                            'end_date': date - timedelta(days=1),
                            'length_days': period_length,
                            'avg_coverage': daily_stats.loc[start_date:date-timedelta(days=1), 'coverage_percentage'].mean(),
                            'min_coverage': daily_stats.loc[start_date:date-timedelta(days=1), 'coverage_percentage'].min(),
                            'avg_devices': daily_stats.loc[start_date:date-timedelta(days=1), 'active_devices'].mean()
                        })
                    start_date = None
        
        # Check if the last period extends to the end
        if start_date is not None:
            period_length = (daily_stats.index[-1] - start_date).days
            if period_length >= min_length:
                periods.append({
                    'start_date': start_date,
                    'end_date': daily_stats.index[-1],
                    'length_days': period_length,
                    'avg_coverage': daily_stats.loc[start_date:, 'coverage_percentage'].mean(),
                    'min_coverage': daily_stats.loc[start_date:, 'coverage_percentage'].min(),
                    'avg_devices': daily_stats.loc[start_date:, 'active_devices'].mean()
                })
        
        return periods
    
    # Find periods with different coverage thresholds
    coverage_thresholds = [90, 80, 70, 60]
    all_periods = {}
    
    for threshold in coverage_thresholds:
        periods = find_continuous_periods(daily_stats, min_coverage=threshold, min_length=min_days)
        all_periods[threshold] = periods
        
        print(f"\nPeriods with ≥{threshold}% device coverage (≥{min_days} days):")
        if periods:
            for i, period in enumerate(periods, 1):
                print(f"  Period {i}: {period['start_date'].strftime('%Y-%m-%d')} to {period['end_date'].strftime('%Y-%m-%d')}")
                print(f"    Length: {period['length_days']} days")
                print(f"    Avg coverage: {period['avg_coverage']:.1f}%")
                print(f"    Min coverage: {period['min_coverage']:.1f}%")
                print(f"    Avg active devices: {period['avg_devices']:.1f}")
        else:
            print(f"  No periods found with {threshold}% coverage")
    
    # ============================================================================
    # 4. DETAILED ANALYSIS OF TOP PERIODS
    # ============================================================================
    
    print("\n4. DETAILED ANALYSIS OF RECOMMENDED PERIODS...")
    
    # Combine all good periods and rank them
    ranked_periods = []
    for threshold, periods in all_periods.items():
        for period in periods:
            score = (period['avg_coverage'] * 0.4 + 
                    period['min_coverage'] * 0.3 + 
                    min(period['length_days']/ideal_days, 1) * 100 * 0.3)
            period['quality_score'] = score
            period['coverage_threshold'] = threshold
            ranked_periods.append(period)
    
    # Sort by quality score
    ranked_periods.sort(key=lambda x: x['quality_score'], reverse=True)
    
    print(f"\nTOP 5 RECOMMENDED PERIODS (ranked by quality score):")
    top_periods = ranked_periods[:5]
    
    detailed_period_analysis = []
    
    for i, period in enumerate(top_periods, 1):
        print(f"\n--- RANK {i} ---")
        print(f"Period: {period['start_date'].strftime('%Y-%m-%d')} to {period['end_date'].strftime('%Y-%m-%d')}")
        print(f"Duration: {period['length_days']} days")
        print(f"Quality Score: {period['quality_score']:.1f}/100")
        print(f"Average Coverage: {period['avg_coverage']:.1f}%")
        print(f"Minimum Coverage: {period['min_coverage']:.1f}%")
        
        # Detailed analysis for this period
        period_start = period['start_date']
        period_end = period['end_date']
        
        # Filter data for this period
        period_data = df[(df[time_col].dt.date >= period_start.date()) & 
                        (df[time_col].dt.date <= period_end.date())]
        
        # Device-level analysis for this period
        device_period_stats = period_data.groupby('device_name').agg({
            'Temperature': ['count', lambda x: x.notna().sum()],
            'Humidity': ['count', lambda x: x.notna().sum()],
            time_col: ['min', 'max']
        })
        
        device_period_stats.columns = ['temp_total', 'temp_valid', 'hum_total', 'hum_valid', 'first_reading', 'last_reading']
        device_period_stats['temp_completeness'] = (device_period_stats['temp_valid'] / device_period_stats['temp_total'] * 100).fillna(0)
        device_period_stats['hum_completeness'] = (device_period_stats['hum_valid'] / device_period_stats['hum_total'] * 100).fillna(0)
        device_period_stats['days_active'] = (device_period_stats['last_reading'] - device_period_stats['first_reading']).dt.days + 1
        
        print(f"Devices with data: {len(device_period_stats)}/{df['device_name'].nunique()}")
        print(f"Average temperature completeness: {device_period_stats['temp_completeness'].mean():.1f}%")
        print(f"Average humidity completeness: {device_period_stats['hum_completeness'].mean():.1f}%")
        
        # Identify devices with excellent data for this period
        excellent_devices = device_period_stats[
            (device_period_stats['temp_completeness'] >= 95) & 
            (device_period_stats['hum_completeness'] >= 95) &
            (device_period_stats['days_active'] >= period['length_days'] * 0.8)
        ]
        
        print(f"Devices with excellent data quality (≥95% complete): {len(excellent_devices)}")
        if len(excellent_devices) > 0:
            print("Excellent devices:", list(excellent_devices.index[:10]))  # Show first 10
        
        detailed_period_analysis.append({
            'period': period,
            'device_stats': device_period_stats,
            'excellent_devices': list(excellent_devices.index),
            'period_data': period_data
        })
    
    # ============================================================================
    # 5. DATA COMPLETENESS HEATMAP ANALYSIS
    # ============================================================================
    
    print("\n5. GENERATING DATA COMPLETENESS ANALYSIS...")
    
    # Create weekly completeness matrix
    df['week'] = df[time_col].dt.to_period('W')
    weekly_completeness = df.groupby(['week', 'device_name']).agg({
        'Temperature': lambda x: x.notna().sum() / len(x) * 100,
        'Humidity': lambda x: x.notna().sum() / len(x) * 100
    }).unstack(fill_value=0)
    
    # Calculate overall weekly scores
    weekly_scores = pd.DataFrame(index=weekly_completeness.index)
    weekly_scores['avg_temp_completeness'] = weekly_completeness['Temperature'].mean(axis=1)
    weekly_scores['avg_hum_completeness'] = weekly_completeness['Humidity'].mean(axis=1)
    weekly_scores['devices_with_data'] = (weekly_completeness['Temperature'] > 0).sum(axis=1)
    weekly_scores['overall_score'] = (weekly_scores['avg_temp_completeness'] + weekly_scores['avg_hum_completeness']) / 2
    
    print(f"✓ Weekly completeness analysis generated")
    print(f"Best week: {weekly_scores['overall_score'].idxmax()} (Score: {weekly_scores['overall_score'].max():.1f})")
    
    # ============================================================================
    # 6. FINAL RECOMMENDATIONS
    # ============================================================================
    
    print("\n6. FINAL RECOMMENDATIONS FOR SPATIAL ANALYSIS")
    print("="*60)
    
    if ranked_periods:
        best_period = ranked_periods[0]
        
        print(f"\n🎯 PRIMARY RECOMMENDATION:")
        print(f"   Period: {best_period['start_date'].strftime('%Y-%m-%d')} to {best_period['end_date'].strftime('%Y-%m-%d')}")
        print(f"   Duration: {best_period['length_days']} days")
        print(f"   Quality Score: {best_period['quality_score']:.1f}/100")
        print(f"   Device Coverage: {best_period['avg_coverage']:.1f}% average, {best_period['min_coverage']:.1f}% minimum")
        
        # Get devices with excellent data for recommended period
        best_period_analysis = detailed_period_analysis[0]
        excellent_devices = best_period_analysis['excellent_devices']
        
        print(f"   Recommended devices for spatial analysis: {len(excellent_devices)} devices")
        print(f"   Device list: {', '.join(excellent_devices[:15])}{'...' if len(excellent_devices) > 15 else ''}")
        
        if best_period['length_days'] >= ideal_days:
            print(f"   ✅ Meets ideal duration requirement ({ideal_days}+ days)")
        elif best_period['length_days'] >= min_days:
            print(f"   ⚠️ Meets minimum duration requirement ({min_days}+ days)")
        else:
            print(f"   ❌ Below minimum duration requirement")
        
        print(f"\n📊 ALTERNATIVE OPTIONS:")
        for i, period in enumerate(ranked_periods[1:4], 2):
            print(f"   Option {i}: {period['start_date'].strftime('%Y-%m-%d')} to {period['end_date'].strftime('%Y-%m-%d')} ")
            print(f"            ({period['length_days']} days, {period['avg_coverage']:.1f}% coverage)")
    
    else:
        print("❌ No suitable periods found with current criteria")
        print("💡 Consider lowering coverage requirements or minimum duration")
    
    # ============================================================================
    # 7. RETURN COMPREHENSIVE RESULTS
    # ============================================================================
    
    results = {
        'daily_stats': daily_stats,
        'device_availability': device_availability,
        'all_periods': all_periods,
        'ranked_periods': ranked_periods,
        'detailed_analysis': detailed_period_analysis,
        'weekly_completeness': weekly_completeness,
        'weekly_scores': weekly_scores,
        'recommendations': {
            'best_period': ranked_periods[0] if ranked_periods else None,
            'alternative_periods': ranked_periods[1:5] if len(ranked_periods) > 1 else [],
            'excellent_devices': excellent_devices if ranked_periods else [],
        },
        'original_data': df
    }
    
    print(f"\n{'='*80}")
    print("TEMPORAL ANALYSIS COMPLETE")
    print(f"{'='*80}")
    
    return results

def create_temporal_visualizations(analysis_results, save_plots=False):
    """
    Create comprehensive visualizations for temporal analysis.
    """
    
    daily_stats = analysis_results['daily_stats']
    device_availability = analysis_results['device_availability']
    ranked_periods = analysis_results['ranked_periods']
    
    # Create a comprehensive dashboard
    fig = plt.figure(figsize=(20, 16))
    
    # 1. Device Coverage Over Time
    ax1 = plt.subplot(3, 2, 1)
    daily_stats['coverage_percentage'].plot(ax=ax1, linewidth=2, color='blue')
    ax1.set_title('Daily Device Coverage Percentage', fontsize=14, fontweight='bold')
    ax1.set_ylabel('Coverage %')
    ax1.grid(True, alpha=0.3)
    ax1.axhline(y=80, color='red', linestyle='--', alpha=0.7, label='80% threshold')
    ax1.axhline(y=70, color='orange', linestyle='--', alpha=0.7, label='70% threshold')
    ax1.legend()
    
    # 2. Number of Active Devices Over Time
    ax2 = plt.subplot(3, 2, 2)
    daily_stats['active_devices'].plot(ax=ax2, linewidth=2, color='green')
    ax2.set_title('Number of Active Devices per Day', fontsize=14, fontweight='bold')
    ax2.set_ylabel('Active Devices')
    ax2.grid(True, alpha=0.3)
    
    # 3. Device Availability Heatmap
    ax3 = plt.subplot(3, 2, (3, 4))

    # Sample the heatmap for better visualization (every 7th day)
    sample_dates = device_availability.index[::7]
    sample_data = device_availability.loc[sample_dates]

    # Convert to numeric to avoid dtype object error
    sample_data_numeric = sample_data.astype(float)

    sns.heatmap(sample_data_numeric.T, ax=ax3, cmap='RdYlGn', cbar_kws={'label': 'Device Active (1=Yes, 0=No)'},
                xticklabels=[d.strftime('%m-%d') for d in sample_dates[::4]], yticklabels=True)
    ax3.set_title('Device Activity Heatmap (Sampled)', fontsize=14, fontweight='bold')
    ax3.set_xlabel('Date')
    ax3.set_ylabel('Device Name')
    
    # 4. Period Quality Scores
    ax4 = plt.subplot(3, 2, 5)
    if ranked_periods:
        period_names = [f"{p['start_date'].strftime('%m-%d')} to {p['end_date'].strftime('%m-%d')}" 
                       for p in ranked_periods[:10]]
        quality_scores = [p['quality_score'] for p in ranked_periods[:10]]
        
        bars = ax4.bar(range(len(quality_scores)), quality_scores, color='skyblue')
        ax4.set_title('Top 10 Period Quality Scores', fontsize=14, fontweight='bold')
        ax4.set_ylabel('Quality Score')
        ax4.set_xlabel('Period')
        ax4.set_xticks(range(len(period_names)))
        ax4.set_xticklabels(period_names, rotation=45, ha='right')
        
        # Highlight the best period
        if bars:
            bars[0].set_color('gold')
    
    # 5. Coverage Distribution
    ax5 = plt.subplot(3, 2, 6)
    daily_stats['coverage_percentage'].hist(bins=20, ax=ax5, alpha=0.7, color='purple')
    ax5.set_title('Distribution of Daily Coverage Percentages', fontsize=14, fontweight='bold')
    ax5.set_xlabel('Coverage %')
    ax5.set_ylabel('Frequency (days)')
    ax5.axvline(x=daily_stats['coverage_percentage'].mean(), color='red', 
               linestyle='--', label=f'Mean: {daily_stats["coverage_percentage"].mean():.1f}%')
    ax5.legend()
    
    plt.tight_layout()
    
    if save_plots:
        plt.savefig('temporal_analysis_dashboard.png', dpi=300, bbox_inches='tight')
        print("✓ Temporal analysis dashboard saved as 'temporal_analysis_dashboard.png'")
    
    plt.show()
    
    # Create a focused plot for the best period
    if ranked_periods:
        best_period = ranked_periods[0]
        detailed_analysis = analysis_results['detailed_analysis'][0]
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
        
        # Plot device completeness for the best period
        device_stats = detailed_analysis['device_stats']
        
        x_pos = range(len(device_stats))
        ax1.bar(x_pos, device_stats['temp_completeness'], alpha=0.7, label='Temperature', color='red')
        ax1.bar(x_pos, device_stats['hum_completeness'], alpha=0.7, label='Humidity', color='blue')
        ax1.set_title(f'Data Completeness for Best Period: {best_period["start_date"].strftime("%Y-%m-%d")} to {best_period["end_date"].strftime("%Y-%m-%d")}',
                     fontsize=14, fontweight='bold')
        ax1.set_ylabel('Completeness %')
        ax1.set_xlabel('Device')
        ax1.set_xticks(x_pos[::2])  # Show every other device name
        ax1.set_xticklabels(device_stats.index[::2], rotation=45, ha='right')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.axhline(y=95, color='green', linestyle='--', alpha=0.7, label='95% threshold')
        
        # Plot daily coverage for the best period
        period_start = best_period['start_date']
        period_end = best_period['end_date']
        period_daily = daily_stats.loc[period_start:period_end]
        
        period_daily['coverage_percentage'].plot(ax=ax2, linewidth=3, color='green', marker='o')
        ax2.set_title('Daily Coverage During Best Period', fontsize=14, fontweight='bold')
        ax2.set_ylabel('Coverage %')
        ax2.set_xlabel('Date')
        ax2.grid(True, alpha=0.3)
        ax2.axhline(y=period_daily['coverage_percentage'].mean(), color='red', 
                   linestyle='--', alpha=0.7, label=f'Period Average: {period_daily["coverage_percentage"].mean():.1f}%')
        ax2.legend()
        
        plt.tight_layout()
        
        if save_plots:
            plt.savefig('best_period_analysis.png', dpi=300, bbox_inches='tight')
            print("✓ Best period analysis saved as 'best_period_analysis.png'")
        
        plt.show()

# ============================================================================
# USAGE EXAMPLE
# ============================================================================

if __name__ == "__main__":
    # Replace with your CSV file path
    csv_file_path = "Data/filtered_devices_iot_sensor_data_2025-06-06_corrected_time.csv"
    
    # Run the advanced temporal analysis
    results = advanced_temporal_analysis_for_spatial(csv_file_path, min_days=7, ideal_days=14)
    
    # Create visualizations
    create_temporal_visualizations(results, save_plots=True)
    
    # Access the recommendations
    if results['recommendations']['best_period']:
        print("\n" + "="*60)
        print("QUICK ACCESS TO RESULTS:")
        print("="*60)
        
        best = results['recommendations']['best_period']
        print(f"Best period: {best['start_date']} to {best['end_date']}")
        print(f"Duration: {best['length_days']} days")
        print(f"Quality score: {best['quality_score']:.1f}")
        print(f"Recommended devices: {len(results['recommendations']['excellent_devices'])}")
        
        # Extract data for the best period for further analysis
        period_data = results['detailed_analysis'][0]['period_data']
        excellent_devices = results['recommendations']['excellent_devices']
        
        # Filter to only excellent devices
        spatial_analysis_data = period_data[period_data['device_name'].isin(excellent_devices)]
        
        print(f"Data ready for spatial analysis: {len(spatial_analysis_data):,} records")
        print(f"Devices ready for spatial analysis: {len(excellent_devices)}")
        
        # Save the filtered data for spatial analysis
        output_filename = f"spatial_analysis_data_{best['start_date'].strftime('%Y%m%d')}_{best['end_date'].strftime('%Y%m%d')}.csv"
        spatial_analysis_data.to_csv(output_filename, index=False)
        print(f"✓ Spatial analysis data saved as: {output_filename}")
