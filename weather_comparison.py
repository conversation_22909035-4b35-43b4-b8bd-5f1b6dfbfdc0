import pandas as pd
import requests
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import seaborn as sns

# Set the style for better-looking plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def load_sensor_data(csv_file):
    """Load and process local sensor data from CSV file"""
    print("Loading local sensor data...")
    
    # Read CSV file
    df = pd.read_csv(csv_file)
    
    # Convert timestamp to datetime
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    
    # Convert timezone to UTC for consistency
    df['timestamp'] = df['timestamp'].dt.tz_convert('UTC')
    
    print(f"Loaded {len(df)} sensor readings")
    print(f"Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
    
    return df

def fetch_weather_station_data(url):
    """Fetch weather data from Open-Meteo API"""
    print("Fetching weather station data...")
    
    try:
        response = requests.get(url)
        response.raise_for_status()
        data = response.json()
        
        # Extract hourly data
        hourly_data = data['hourly']
        
        # Create DataFrame
        df = pd.DataFrame({
            'timestamp': pd.to_datetime(hourly_data['time']),
            'temperature': hourly_data['temperature_2m'],
            'humidity': hourly_data['relative_humidity_2m']
        })
        
        # Convert to UTC (Open-Meteo returns data in the specified timezone)
        df['timestamp'] = df['timestamp'].dt.tz_localize('America/New_York').dt.tz_convert('UTC')
        
        print(f"Fetched {len(df)} weather station readings")
        print(f"Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
        
        return df
        
    except requests.exceptions.RequestException as e:
        print(f"Error fetching weather data: {e}")
        return None
    except KeyError as e:
        print(f"Error parsing weather data: {e}")
        return None

def plot_comparison(sensor_df, weather_df):
    """Create comparison plots for temperature and humidity"""
    
    # Create figure with subplots
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
    fig.suptitle('Local Sensor vs Weather Station Data Comparison', fontsize=16, fontweight='bold')
    
    # Temperature comparison
    ax1.plot(sensor_df['timestamp'], sensor_df['Temperature'], 
             label='Local Sensor', linewidth=2, alpha=0.8, marker='o', markersize=3)
    ax1.plot(weather_df['timestamp'], weather_df['temperature'], 
             label='Weather Station', linewidth=2, alpha=0.8, marker='s', markersize=3)
    
    ax1.set_title('Temperature Comparison', fontsize=14, fontweight='bold')
    ax1.set_ylabel('Temperature (°C)', fontsize=12)
    ax1.legend(fontsize=11)
    ax1.grid(True, alpha=0.3)
    
    # Format x-axis for temperature plot
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
    ax1.xaxis.set_major_locator(mdates.DayLocator(interval=1))
    plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
    
    # Humidity comparison
    ax2.plot(sensor_df['timestamp'], sensor_df['Humidity'], 
             label='Local Sensor', linewidth=2, alpha=0.8, marker='o', markersize=3)
    ax2.plot(weather_df['timestamp'], weather_df['humidity'], 
             label='Weather Station', linewidth=2, alpha=0.8, marker='s', markersize=3)
    
    ax2.set_title('Humidity Comparison', fontsize=14, fontweight='bold')
    ax2.set_ylabel('Relative Humidity (%)', fontsize=12)
    ax2.set_xlabel('Date and Time (UTC)', fontsize=12)
    ax2.legend(fontsize=11)
    ax2.grid(True, alpha=0.3)
    
    # Format x-axis for humidity plot
    ax2.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
    ax2.xaxis.set_major_locator(mdates.DayLocator(interval=1))
    plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)
    
    # Adjust layout to prevent label cutoff
    plt.tight_layout()
    plt.show()

def save_weather_data_to_csv(weather_df, filename='weather_station_data.csv'):
    """Convert weather station data to CSV with same structure as sensor data"""
    print(f"Saving weather station data to {filename}...")
    
    # Create a copy of the dataframe to avoid modifying the original
    weather_csv = weather_df.copy()
    
    # Rename columns to match sensor data structure
    weather_csv = weather_csv.rename(columns={
        'temperature': 'Temperature',
        'humidity': 'Humidity'
    })
    
    # Convert timestamp to the same format as sensor data (with timezone)
    # Convert back to the original timezone for consistency with sensor data
    weather_csv['timestamp'] = weather_csv['timestamp'].dt.tz_convert('America/New_York')
    
    # Format timestamp to match sensor data format
    weather_csv['timestamp'] = weather_csv['timestamp'].dt.strftime('%Y-%m-%d %H:%M:%S%z')
    
    # Reorder columns to match sensor data
    weather_csv = weather_csv[['timestamp', 'Temperature', 'Humidity']]
    
    # Save to CSV
    weather_csv.to_csv(filename, index=False)
    
    print(f"Weather station data saved to {filename}")
    print(f"Structure: {len(weather_csv)} rows, columns: {list(weather_csv.columns)}")
    print(f"Sample data:")
    print(weather_csv.head(3).to_string(index=False))
    
    return filename

def calculate_statistics(sensor_df, weather_df):
    """Calculate and display comparison statistics"""
    print("\n" + "="*50)
    print("COMPARISON STATISTICS")
    print("="*50)
    
    # Find common time range
    start_time = max(sensor_df['timestamp'].min(), weather_df['timestamp'].min())
    end_time = min(sensor_df['timestamp'].max(), weather_df['timestamp'].max())
    
    print(f"Common data period: {start_time} to {end_time}")
    
    # Filter data to common time range
    sensor_common = sensor_df[(sensor_df['timestamp'] >= start_time) & 
                             (sensor_df['timestamp'] <= end_time)]
    weather_common = weather_df[(weather_df['timestamp'] >= start_time) & 
                               (weather_df['timestamp'] <= end_time)]
    
    # Temperature statistics
    print(f"\nTemperature:")
    print(f"  Local Sensor - Mean: {sensor_common['Temperature'].mean():.2f}°C, "
          f"Min: {sensor_common['Temperature'].min():.2f}°C, "
          f"Max: {sensor_common['Temperature'].max():.2f}°C")
    print(f"  Weather Station - Mean: {weather_common['temperature'].mean():.2f}°C, "
          f"Min: {weather_common['temperature'].min():.2f}°C, "
          f"Max: {weather_common['temperature'].max():.2f}°C")
    
    temp_diff = sensor_common['Temperature'].mean() - weather_common['temperature'].mean()
    print(f"  Average difference: {temp_diff:.2f}°C (sensor vs station)")
    
    # Humidity statistics
    print(f"\nHumidity:")
    print(f"  Local Sensor - Mean: {sensor_common['Humidity'].mean():.1f}%, "
          f"Min: {sensor_common['Humidity'].min():.1f}%, "
          f"Max: {sensor_common['Humidity'].max():.1f}%")
    print(f"  Weather Station - Mean: {weather_common['humidity'].mean():.1f}%, "
          f"Min: {weather_common['humidity'].min():.1f}%, "
          f"Max: {weather_common['humidity'].max():.1f}%")
    
    humidity_diff = sensor_common['Humidity'].mean() - weather_common['humidity'].mean()
    print(f"  Average difference: {humidity_diff:.1f}% (sensor vs station)")

def create_weather_csv_only(api_url, output_filename='weather_station_data.csv'):
    """Standalone function to just fetch weather data and save as CSV"""
    print("Fetching weather station data and saving to CSV...")
    print("="*50)
    
    # Fetch weather station data
    weather_data = fetch_weather_station_data(api_url)
    
    if weather_data is None:
        print("Failed to fetch weather station data.")
        return None
    
    # Save to CSV
    csv_filename = save_weather_data_to_csv(weather_data, output_filename)
    
    print(f"\nSuccess! Weather station data saved to '{csv_filename}'")
    print("You can now use this CSV file for further analysis or comparison.")
    
    return csv_filename

def main():
    """Main function to run the comparison analysis"""
    print("Weather Data Comparison Tool")
    print("="*30)
    
    # Configuration
    csv_file = 'iot_sensor_data_for_web_app_hourly.csv'  # Update this path if needed
    api_url = 'https://archive-api.open-meteo.com/v1/archive?latitude=46.782835771899464&longitude=-71.27038736335324&start_date=2025-03-10&end_date=2025-03-20&hourly=temperature_2m,relative_humidity_2m&timezone=America/New_York'
    
    # Ask user what they want to do
    print("\nChoose an option:")
    print("1. Full comparison (fetch weather data, save CSV, and create plots)")
    print("2. Just create weather station CSV file")
    
    choice = input("Enter your choice (1 or 2): ").strip()
    
    if choice == '2':
        # Just create the CSV file
        create_weather_csv_only(api_url)
        return
    
    # Default to full comparison (option 1)
    print("\nRunning full comparison analysis...")
    
    # Load local sensor data
    sensor_data = load_sensor_data(csv_file)
    
    # Fetch weather station data
    weather_data = fetch_weather_station_data(api_url)
    
    if weather_data is None:
        print("Failed to fetch weather station data. Exiting.")
        return
    
    # Save weather station data to CSV file
    weather_csv_file = save_weather_data_to_csv(weather_data)
    
    # Create comparison plots
    plot_comparison(sensor_data, weather_data)
    
    # Calculate and display statistics
    calculate_statistics(sensor_data, weather_data)
    
    print(f"\nAnalysis complete! Compared {len(sensor_data)} sensor readings with {len(weather_data)} weather station readings.")
    print(f"Weather station data also saved to '{weather_csv_file}' for future use.")

# Alternative: If you just want to create the CSV without running the full script
if __name__ == "__main__":
    main()
    
    # Uncomment the lines below if you just want to create the weather CSV file:
    # api_url = 'https://archive-api.open-meteo.com/v1/archive?latitude=46.782835771899464&longitude=-71.27038736335324&start_date=2025-03-10&end_date=2025-03-20&hourly=temperature_2m,relative_humidity_2m&timezone=America/New_York'
    # create_weather_csv_only(api_url, 'my_weather_station_data.csv')