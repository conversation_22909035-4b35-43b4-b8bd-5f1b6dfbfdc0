import arcpy
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import sys

def setup_environment(workspace_path, output_gdb):
    """Set up ArcGIS environment and create geodatabase if needed"""
    try:
        # Set workspace
        arcpy.env.workspace = workspace_path
        arcpy.env.overwriteOutput = True
        
        # Create geodatabase if it doesn't exist
        gdb_path = os.path.join(workspace_path, output_gdb)
        if not arcpy.Exists(gdb_path):
            arcpy.management.CreateFileGDB(workspace_path, output_gdb)
            print(f"✓ Created geodatabase: {gdb_path}")
        
        return gdb_path
    except Exception as e:
        print(f"❌ Error setting up environment: {e}")
        return None

def read_sensor_locations(shapefile_path):
    """Read sensor shapefile and extract spatial information"""
    try:
        print(f"📍 Reading sensor locations from: {shapefile_path}")
        
        # Convert shapefile to pandas dataframe
        sensor_fields = ['Name', 'X_coordinate', 'Y_coordinate', 'Description', 'ID']
        
        # Use arcpy to read shapefile data
        sensor_data = []
        with arcpy.da.SearchCursor(shapefile_path, sensor_fields + ['SHAPE@X', 'SHAPE@Y']) as cursor:
            for row in cursor:
                sensor_data.append({
                    'SensorID': row[0],  # Name field
                    'X_Coordinate': row[1] if row[1] else row[5],  # Use X_coordinate or SHAPE@X
                    'Y_Coordinate': row[2] if row[2] else row[6],  # Use Y_coordinate or SHAPE@Y  
                    'Location_Description': row[3] if row[3] else '',
                    'EUI_ID': row[4] if row[4] else '',
                    'Sensor_Type': 'LHT65N' if 'LHT65N' in str(row[0]) else 'LSN50' if 'LSN50' in str(row[0]) else 'Unknown'
                })
        
        sensor_df = pd.DataFrame(sensor_data)
        print(f"✓ Found {len(sensor_df)} sensors in shapefile")
        print(f"✓ Sensor types: {sensor_df['Sensor_Type'].value_counts().to_dict()}")
        
        return sensor_df
        
    except Exception as e:
        print(f"❌ Error reading sensor locations: {e}")
        return None

def read_measurement_data(csv_path):
    """Read and process measurement CSV data"""
    try:
        print(f"📊 Reading measurement data from: {csv_path}")
        
        # Read CSV with proper data types
        measurements_df = pd.read_csv(csv_path, 
                                    dtype={
                                        'device_id': str,
                                        'device_name': str, 
                                        'Humidity': float,
                                        'Temperature': str  # Keep as string initially to handle mixed types
                                    })
        
        print(f"✓ Loaded {len(measurements_df)} measurement records")
        
        # Clean and convert temperature
        measurements_df['Temperature'] = pd.to_numeric(measurements_df['Temperature'], errors='coerce')
        
        # Convert time field
        measurements_df['DateTime'] = pd.to_datetime(measurements_df['cleaned_time'], errors='coerce')
        
        # Remove invalid records
        initial_count = len(measurements_df)
        measurements_df = measurements_df.dropna(subset=['DateTime', 'Temperature', 'Humidity', 'device_name'])
        print(f"✓ Cleaned data: {len(measurements_df)} valid records (removed {initial_count - len(measurements_df)} invalid)")
        
        # Data quality summary
        print(f"✓ Date range: {measurements_df['DateTime'].min()} to {measurements_df['DateTime'].max()}")
        print(f"✓ Temperature range: {measurements_df['Temperature'].min():.2f}°C to {measurements_df['Temperature'].max():.2f}°C")
        print(f"✓ Humidity range: {measurements_df['Humidity'].min():.1f}% to {measurements_df['Humidity'].max():.1f}%")
        print(f"✓ Unique sensors in data: {measurements_df['device_name'].nunique()}")
        
        return measurements_df
        
    except Exception as e:
        print(f"❌ Error reading measurement data: {e}")
        return None

def create_hourly_aggregates(measurements_df):
    """Aggregate measurements to hourly intervals"""
    try:
        print("⏰ Creating hourly aggregates...")
        
        # Round datetime to nearest hour
        measurements_df['DateTime_Hour'] = measurements_df['DateTime'].dt.round('H')
        
        # Group by sensor and hour, calculate statistics
        hourly_stats = measurements_df.groupby(['device_name', 'DateTime_Hour']).agg({
            'Temperature': ['mean', 'std', 'count'],
            'Humidity': ['mean', 'std', 'count'],
            'device_id': 'first'  # Get the device_id for joining
        }).reset_index()
        
        # Flatten column names
        hourly_stats.columns = [
            'SensorID', 'DateTime', 
            'Temperature', 'Temperature_Std', 'Temperature_Count',
            'Humidity', 'Humidity_Std', 'Humidity_Count',
            'device_id'
        ]
        
        # Round values for cleaner output
        hourly_stats['Temperature'] = hourly_stats['Temperature'].round(2)
        hourly_stats['Humidity'] = hourly_stats['Humidity'].round(2)
        hourly_stats['Temperature_Std'] = hourly_stats['Temperature_Std'].round(3)
        hourly_stats['Humidity_Std'] = hourly_stats['Humidity_Std'].round(3)
        
        print(f"✓ Created {len(hourly_stats)} hourly records")
        print(f"✓ Time span: {hourly_stats['DateTime'].min()} to {hourly_stats['DateTime'].max()}")
        
        # Check for missing hours
        expected_hours = pd.date_range(
            start=hourly_stats['DateTime'].min(),
            end=hourly_stats['DateTime'].max(), 
            freq='H'
        )
        sensors = hourly_stats['SensorID'].unique()
        expected_records = len(expected_hours) * len(sensors)
        actual_records = len(hourly_stats)
        print(f"✓ Data completeness: {actual_records}/{expected_records} ({100*actual_records/expected_records:.1f}%)")
        
        return hourly_stats
        
    except Exception as e:
        print(f"❌ Error creating hourly aggregates: {e}")
        return None

def merge_spatial_temporal(sensor_df, hourly_df):
    """Merge spatial and temporal data"""
    try:
        print("🔗 Merging spatial and temporal data...")
        
        # Merge on sensor name/ID
        merged_df = hourly_df.merge(
            sensor_df, 
            on='SensorID', 
            how='inner'  # Only keep sensors that exist in both datasets
        )
        
        print(f"✓ Successfully merged {len(merged_df)} records")
        print(f"✓ Covering {merged_df['SensorID'].nunique()} sensors")
        
        # Add additional time-based fields for analysis
        merged_df['Year'] = merged_df['DateTime'].dt.year
        merged_df['Month'] = merged_df['DateTime'].dt.month
        merged_df['Day'] = merged_df['DateTime'].dt.day
        merged_df['Hour'] = merged_df['DateTime'].dt.hour
        merged_df['DayOfWeek'] = merged_df['DateTime'].dt.dayofweek
        merged_df['IsWeekend'] = merged_df['DayOfWeek'].isin([5, 6]).astype(int)
        
        # Format datetime for ArcGIS compatibility
        merged_df['DateTime_String'] = merged_df['DateTime'].dt.strftime('%Y-%m-%d %H:%M:%S')
        
        return merged_df
        
    except Exception as e:
        print(f"❌ Error merging data: {e}")
        return None

def create_time_enabled_feature_class(merged_df, output_gdb, output_name):
    """Create time-enabled feature class in ArcGIS"""
    try:
        print("🗺️ Creating time-enabled feature class...")
        
        # Define output feature class path
        output_fc = os.path.join(output_gdb, output_name)
        
        # Create a temporary CSV for XY Table to Point
        temp_csv = os.path.join(os.path.dirname(output_gdb), "temp_spatiotemporal_data.csv")
        merged_df.to_csv(temp_csv, index=False)
        print(f"✓ Exported temporary CSV: {temp_csv}")
        
        # Create XY Event Layer
        temp_layer = "SensorData_XY_Layer"
        arcpy.management.MakeXYEventLayer(
            table=temp_csv,
            in_x_field="X_Coordinate",
            in_y_field="Y_Coordinate", 
            out_layer=temp_layer,
            spatial_reference=arcpy.SpatialReference(2949)  # NAD83 CSRS MTM-7
        )
        print("✓ Created XY event layer")
        
        # Copy features to permanent feature class
        arcpy.management.CopyFeatures(temp_layer, output_fc)
        print(f"✓ Created feature class: {output_fc}")
        
        # Enable time properties
        arcpy.management.EnableTime(
            in_layer=output_fc,
            time_field="DateTime_String",
            time_field_format="yyyy-MM-dd HH:mm:ss",
            time_reference=None
        )
        print("✓ Enabled time properties")
        
        # Add indexes for better performance
        try:
            arcpy.management.AddIndex(output_fc, ["SensorID"], "IDX_SensorID")
            arcpy.management.AddIndex(output_fc, ["DateTime_String"], "IDX_DateTime") 
            arcpy.management.AddIndex(output_fc, ["Sensor_Type"], "IDX_SensorType")
            print("✓ Added spatial and attribute indexes")
        except:
            print("⚠️ Warning: Could not add some indexes (may already exist)")
        
        # Clean up temporary files
        try:
            os.remove(temp_csv)
            arcpy.management.Delete(temp_layer)
            print("✓ Cleaned up temporary files")
        except:
            pass
        
        return output_fc
        
    except Exception as e:
        print(f"❌ Error creating feature class: {e}")
        return None

def generate_summary_report(output_fc, merged_df):
    """Generate summary report of the created dataset"""
    try:
        print("\n" + "="*60)
        print("📋 SPATIOTEMPORAL DATASET SUMMARY REPORT")
        print("="*60)
        
        # Dataset overview
        print(f"📁 Output Feature Class: {output_fc}")
        print(f"📊 Total Records: {len(merged_df):,}")
        print(f"📍 Unique Sensors: {merged_df['SensorID'].nunique()}")
        print(f"⏰ Time Period: {merged_df['DateTime'].min()} to {merged_df['DateTime'].max()}")
        print(f"⏳ Duration: {(merged_df['DateTime'].max() - merged_df['DateTime'].min()).days} days")
        
        # Sensor type breakdown
        print(f"\n🔍 Sensor Type Distribution:")
        sensor_type_counts = merged_df['SensorID'].apply(lambda x: 'LHT65N' if 'LHT65N' in x else 'LSN50').value_counts()
        for sensor_type, count in sensor_type_counts.items():
            unique_sensors = merged_df[merged_df['SensorID'].str.contains(sensor_type)]['SensorID'].nunique()
            print(f"   {sensor_type}: {unique_sensors} sensors, {count:,} records")
        
        # Data quality metrics
        print(f"\n📈 Data Quality Metrics:")
        print(f"   Temperature Range: {merged_df['Temperature'].min():.2f}°C to {merged_df['Temperature'].max():.2f}°C")
        print(f"   Humidity Range: {merged_df['Humidity'].min():.1f}% to {merged_df['Humidity'].max():.1f}%")
        print(f"   Missing Temperature: {merged_df['Temperature'].isna().sum()} ({100*merged_df['Temperature'].isna().mean():.1f}%)")
        print(f"   Missing Humidity: {merged_df['Humidity'].isna().sum()} ({100*merged_df['Humidity'].isna().mean():.1f}%)")
        
        # Temporal coverage
        hours_per_sensor = merged_df.groupby('SensorID').size()
        print(f"\n⏰ Temporal Coverage per Sensor:")
        print(f"   Mean records per sensor: {hours_per_sensor.mean():.1f}")
        print(f"   Min records per sensor: {hours_per_sensor.min()}")
        print(f"   Max records per sensor: {hours_per_sensor.max()}")
        
        # Analysis readiness
        print(f"\n✅ Analysis Ready For:")
        print(f"   ✓ EK Regression Prediction (time-slice interpolation)")
        print(f"   ✓ Space-Time Cube Analysis")
        print(f"   ✓ Time-enabled visualization")
        print(f"   ✓ Spatiotemporal hotspot analysis")
        print(f"   ✓ PCA and statistical analysis")
        
        print("="*60)
        
    except Exception as e:
        print(f"❌ Error generating summary: {e}")

def main():
    """Main execution function"""
    print("🚀 SPATIOTEMPORAL DATA CONVERSION STARTED")
    print("="*60)
    
    # Configuration - UPDATE THESE PATHS
    config = {
        'workspace_path': r"C:\YourProject",  # UPDATE THIS PATH
        'output_gdb': "SpatiotemporalAnalysis.gdb", 
        'shapefile_path': r"C:\YourProject\ActiveSensors.shp",  # UPDATE THIS PATH
        'csv_path': r"C:\YourProject\filtered_devices_iot_sensor_data_20250606_corrected_time_10_19_March.csv",  # UPDATE THIS PATH
        'output_fc_name': "SensorData_TimeEnabled"
    }
    
    # Verify inputs exist
    for key, path in config.items():
        if key in ['shapefile_path', 'csv_path'] and not os.path.exists(path):
            print(f"❌ ERROR: {key} not found at {path}")
            print("Please update the file paths in the configuration section")
            return False
    
    try:
        # Step 1: Setup environment
        output_gdb = setup_environment(config['workspace_path'], config['output_gdb'])
        if not output_gdb:
            return False
        
        # Step 2: Read sensor locations
        sensor_df = read_sensor_locations(config['shapefile_path'])
        if sensor_df is None:
            return False
        
        # Step 3: Read measurement data  
        measurements_df = read_measurement_data(config['csv_path'])
        if measurements_df is None:
            return False
        
        # Step 4: Create hourly aggregates
        hourly_df = create_hourly_aggregates(measurements_df)
        if hourly_df is None:
            return False
        
        # Step 5: Merge spatial and temporal data
        merged_df = merge_spatial_temporal(sensor_df, hourly_df)
        if merged_df is None:
            return False
        
        # Step 6: Create time-enabled feature class
        output_fc = create_time_enabled_feature_class(
            merged_df, 
            output_gdb, 
            config['output_fc_name']
        )
        if not output_fc:
            return False
        
        # Step 7: Generate summary report
        generate_summary_report(output_fc, merged_df)
        
        print(f"\n🎉 SUCCESS! Time-enabled feature class created:")
        print(f"📁 {output_fc}")
        print(f"\n📝 Next Steps:")
        print(f"   1. Open ArcGIS Pro and add the feature class to your map")
        print(f"   2. Configure time slider in Map Properties")
        print(f"   3. Run EK Regression Prediction for time-slice interpolation")
        print(f"   4. Create Space-Time Cube for advanced analysis")
        
        return True
        
    except Exception as e:
        print(f"❌ CRITICAL ERROR: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ Conversion completed successfully!")
    else:
        print("\n❌ Conversion failed. Please check error messages above.")